"""
Database models for storing shared API configurations in the admin system

This module provides MongoDB/SQLite models for persisting shared API configurations
and integrating them with the Telegram bot admin interface.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from database.connection import get_collection
from models.base import BaseDocument, TimestampMixin, SoftDeleteMixin, now_utc
from shared_api.config.api_config import APIConfiguration
from shared_api.config.registry import api_registry

logger = logging.getLogger(__name__)


class APIConfigEnvironment(str, Enum):
    """Environment types for API configurations"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class APIConfigStatus(str, Enum):
    """Status types for API configurations"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    DEPRECATED = "deprecated"


@dataclass
class APIConfigAuditEntry:
    """Audit trail entry for configuration changes"""
    timestamp: datetime
    user_id: str
    action: str  # created, updated, deleted, enabled, disabled, tested
    changes: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None


class AdminAPIConfiguration(BaseDocument, TimestampMixin, SoftDeleteMixin):
    """
    Database model for storing shared API configurations in the admin system
    
    This model acts as a bridge between the Telegram bot admin interface
    and the shared API system, providing persistence and audit capabilities.
    """
    
    # Basic identification
    name: str  # Unique API name
    display_name: str  # Human-readable name for admin UI
    description: Optional[str] = None
    
    # Shared API configuration (serialized)
    shared_config: Dict[str, Any]  # Serialized APIConfiguration
    
    # Admin-specific metadata
    environment: APIConfigEnvironment = APIConfigEnvironment.DEVELOPMENT
    status: APIConfigStatus = APIConfigStatus.ACTIVE
    tags: List[str] = []
    category: str = "general"
    
    # Ownership and permissions
    created_by: str  # Telegram user ID
    updated_by: Optional[str] = None
    owner: Optional[str] = None  # Primary owner/maintainer
    
    # Configuration flags
    enabled: bool = True
    auto_health_check: bool = True
    allow_public_access: bool = False
    
    # Audit trail
    audit_trail: List[Dict[str, Any]] = []
    
    # Health and monitoring
    last_health_check: Optional[datetime] = None
    health_status: str = "unknown"  # healthy, unhealthy, unknown
    last_error: Optional[str] = None
    
    # Usage statistics
    request_count: int = 0
    last_used: Optional[datetime] = None
    
    @classmethod
    async def from_shared_config(
        cls,
        shared_config: APIConfiguration,
        created_by: str,
        **kwargs
    ) -> "AdminAPIConfiguration":
        """
        Create AdminAPIConfiguration from shared APIConfiguration

        Args:
            shared_config: The shared API configuration
            created_by: Telegram user ID of creator
            **kwargs: Additional admin-specific fields

        Returns:
            AdminAPIConfiguration instance
        """
        # Serialize the shared configuration
        config_dict = shared_config.to_dict()

        # Extract specific fields to avoid duplicate keyword arguments
        display_name = kwargs.pop("display_name", shared_config.name)
        description = kwargs.pop("description", "")

        # Create admin configuration
        admin_config = cls(
            name=shared_config.name,
            display_name=display_name,
            description=description,
            shared_config=config_dict,
            created_by=created_by,
            **kwargs
        )
        
        # Add initial audit entry
        admin_config.add_audit_entry(
            user_id=created_by,
            action="created",
            changes={"name": shared_config.name, "base_url": shared_config.base_url},
            success=True
        )
        
        return admin_config
    
    def to_shared_config(self) -> APIConfiguration:
        """
        Convert to shared APIConfiguration
        
        Returns:
            APIConfiguration instance
        """
        return APIConfiguration.from_dict(self.shared_config)
    
    def update_shared_config(
        self,
        new_config: APIConfiguration,
        updated_by: str,
        changes_description: str = ""
    ) -> None:
        """
        Update the shared configuration
        
        Args:
            new_config: New shared API configuration
            updated_by: Telegram user ID of updater
            changes_description: Description of changes made
        """
        old_config = self.shared_config.copy()
        self.shared_config = new_config.to_dict()
        self.updated_by = updated_by
        self.updated_at = now_utc()
        
        # Add audit entry
        self.add_audit_entry(
            user_id=updated_by,
            action="updated",
            changes={
                "description": changes_description,
                "old_base_url": old_config.get("base_url"),
                "new_base_url": new_config.base_url,
                "endpoints_count": len(new_config.endpoints)
            },
            success=True
        )
    
    def add_audit_entry(
        self,
        user_id: str,
        action: str,
        changes: Dict[str, Any],
        success: bool,
        error_message: Optional[str] = None
    ) -> None:
        """Add an audit trail entry"""
        entry = APIConfigAuditEntry(
            timestamp=now_utc(),
            user_id=user_id,
            action=action,
            changes=changes,
            success=success,
            error_message=error_message
        )
        
        # Convert to dict for storage
        self.audit_trail.append(asdict(entry))
        
        # Keep only last 100 entries
        if len(self.audit_trail) > 100:
            self.audit_trail = self.audit_trail[-100:]
    
    def update_health_status(
        self,
        status: str,
        error_message: Optional[str] = None,
        response_time_ms: Optional[float] = None
    ) -> None:
        """Update health check status"""
        self.health_status = status
        self.last_health_check = now_utc()
        self.last_error = error_message
        
        # Add audit entry for health check
        self.add_audit_entry(
            user_id="system",
            action="health_check",
            changes={
                "status": status,
                "response_time_ms": response_time_ms,
                "error": error_message
            },
            success=(status == "healthy")
        )
    
    def increment_usage(self) -> None:
        """Increment usage statistics"""
        self.request_count += 1
        self.last_used = now_utc()
    
    def to_admin_summary(self) -> Dict[str, Any]:
        """
        Convert to summary format for admin UI
        
        Returns:
            Dictionary with admin UI-friendly data
        """
        shared_config = self.to_shared_config()
        
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "base_url": shared_config.base_url,
            "environment": self.environment,
            "status": self.status,
            "enabled": self.enabled,
            "health_status": self.health_status,
            "endpoints_count": len(shared_config.endpoints),
            "auth_type": shared_config.auth_config.type if shared_config.auth_config else "none",
            "created_at": self.created_at,
            "last_used": self.last_used,
            "request_count": self.request_count,
            "tags": self.tags,
            "category": self.category
        }


class AdminAPIConfigurationService:
    """
    Service for managing admin API configurations with database persistence
    
    This service provides the bridge between the Telegram bot admin interface
    and the shared API system, handling persistence, validation, and integration.
    """
    
    def __init__(self):
        self.collection = get_collection("admin_api_configurations")
        self.health_collection = get_collection("api_health_status")
    
    async def create_api_config(
        self,
        shared_config: APIConfiguration,
        created_by: str,
        **admin_fields
    ) -> AdminAPIConfiguration:
        """
        Create a new admin API configuration
        
        Args:
            shared_config: The shared API configuration
            created_by: Telegram user ID of creator
            **admin_fields: Additional admin-specific fields
            
        Returns:
            Created AdminAPIConfiguration
        """
        try:
            # Create admin configuration
            admin_config = await AdminAPIConfiguration.from_shared_config(
                shared_config, created_by, **admin_fields
            )
            
            # Save to database
            result = await self.collection.insert_one(admin_config.to_mongo())
            admin_config.id = result.inserted_id
            
            # Register with shared API registry
            api_registry.register_api(shared_config)
            
            logger.info(f"Created admin API configuration: {admin_config.name}")
            return admin_config
            
        except Exception as e:
            logger.error(f"Failed to create admin API configuration: {e}")
            raise
    
    async def get_api_config(self, name: str) -> Optional[AdminAPIConfiguration]:
        """Get admin API configuration by name"""
        try:
            doc = await self.collection.find_one({"name": name, "deleted_at": None})
            if doc:
                return AdminAPIConfiguration.from_mongo(doc)
            return None
        except Exception as e:
            logger.error(f"Failed to get admin API configuration '{name}': {e}")
            return None
    
    async def list_api_configs(
        self,
        environment: Optional[str] = None,
        status: Optional[str] = None,
        enabled_only: bool = False
    ) -> List[AdminAPIConfiguration]:
        """List admin API configurations with optional filtering"""
        try:
            filter_dict = {"deleted_at": None}
            
            if environment:
                filter_dict["environment"] = environment
            if status:
                filter_dict["status"] = status
            if enabled_only:
                filter_dict["enabled"] = True
            
            cursor = self.collection.find(filter_dict).sort("name", 1)
            configs = []
            
            async for doc in cursor:
                configs.append(AdminAPIConfiguration.from_mongo(doc))
            
            return configs
            
        except Exception as e:
            logger.error(f"Failed to list admin API configurations: {e}")
            return []
    
    async def update_api_config(
        self,
        name: str,
        shared_config: APIConfiguration,
        updated_by: str,
        changes_description: str = ""
    ) -> bool:
        """Update an existing admin API configuration"""
        try:
            admin_config = await self.get_api_config(name)
            if not admin_config:
                return False
            
            # Update the configuration
            admin_config.update_shared_config(shared_config, updated_by, changes_description)
            
            # Save to database (exclude _id field to avoid immutable field error)
            update_data = admin_config.to_mongo()
            if "_id" in update_data:
                del update_data["_id"]

            await self.collection.update_one(
                {"name": name},
                {"$set": update_data}
            )
            
            # Update shared API registry
            api_registry.register_api(shared_config)
            
            logger.info(f"Updated admin API configuration: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update admin API configuration '{name}': {e}")
            return False
    
    async def delete_api_config(self, name: str, deleted_by: str) -> bool:
        """Soft delete an admin API configuration"""
        try:
            admin_config = await self.get_api_config(name)
            if not admin_config:
                return False
            
            # Add audit entry
            admin_config.add_audit_entry(
                user_id=deleted_by,
                action="deleted",
                changes={"name": name},
                success=True
            )
            
            # Soft delete
            admin_config.deleted_at = now_utc()
            
            # Save to database
            await self.collection.update_one(
                {"name": name},
                {"$set": admin_config.to_mongo()}
            )
            
            # Remove from shared API registry
            if hasattr(api_registry, 'unregister_api'):
                api_registry.unregister_api(name)
            
            logger.info(f"Deleted admin API configuration: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete admin API configuration '{name}': {e}")
            return False


# Global service instance
_admin_api_service = None


def get_admin_api_service() -> AdminAPIConfigurationService:
    """Get the global admin API configuration service instance"""
    global _admin_api_service
    if _admin_api_service is None:
        _admin_api_service = AdminAPIConfigurationService()
    return _admin_api_service
