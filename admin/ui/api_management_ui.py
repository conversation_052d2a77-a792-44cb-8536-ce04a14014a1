"""
UI Components for API Management in Telegram Bot Admin Interface

This module provides reusable UI components for displaying API management
interfaces, keyboards, and formatted messages.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from html import escape

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

logger = logging.getLogger(__name__)


class APIManagementUI:
    """UI components for API management in Telegram bot"""

    @staticmethod
    def _escape(value: Any) -> str:
        """Safely escape user-supplied values for HTML parse mode."""
        if value is None:
            return ""
        if not isinstance(value, str):
            value = str(value)
        return escape(value)
    
    def format_main_menu(self, api_configs: List[Dict[str, Any]]) -> str:
        """Format the main API management menu"""
        total_apis = len(api_configs)
        active_apis = len([config for config in api_configs if config.get("enabled", False)])
        healthy_apis = len([config for config in api_configs if config.get("health_status") == "healthy"])
        
        text = "🔧 <b>API Management System</b>\n\n"
        text += f"📊 <b>Overview:</b>\n"
        text += f"• Total APIs: {total_apis}\n"
        text += f"• Active APIs: {active_apis}\n"
        text += f"• Healthy APIs: {healthy_apis}\n\n"
        
        if api_configs:
            text += "🔗 <b>Recent APIs:</b>\n"
            for config in api_configs[:5]:  # Show first 5
                status_icon = "🟢" if config.get("enabled") else "🔴"
                health_icon = {
                    "healthy": "✅",
                    "unhealthy": "❌",
                    "unknown": "❓"
                }.get(config.get("health_status", "unknown"), "❓")
                
                text += f"{status_icon}{health_icon} {config['display_name']}\n"
                text += f"   <code>{config['base_url']}</code>\n"
        else:
            text += "📝 No API configurations found.\n"
            text += "Use the 'Create New API' button to get started.\n"
        
        text += "\n💡 <b>Quick Actions:</b>\n"
        text += "• Create new API configurations\n"
        text += "• Test API connectivity\n"
        text += "• Monitor API health\n"
        text += "• Manage authentication settings"
        
        return text
    
    def create_main_menu_keyboard(self) -> InlineKeyboardMarkup:
        """Create the main menu keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📋 List All APIs", callback_data="api_list"),
                InlineKeyboardButton(text="➕ Create New API", callback_data="api_create")
            ],
            [
                InlineKeyboardButton(text="🔍 Health Check All", callback_data="api_health_check_all"),
                InlineKeyboardButton(text="🌍 By Environment", callback_data="api_list_env")
            ],
            [
                InlineKeyboardButton(text="📊 Development", callback_data="api_list:development"),
                InlineKeyboardButton(text="🚀 Production", callback_data="api_list:production")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Admin", callback_data="admin:menu")
            ]
        ])
    
    def format_api_list(self, api_configs: List[Dict[str, Any]], environment: Optional[str] = None) -> str:
        """Format the API list display"""
        env_text = f" ({environment.title()})" if environment else ""
        text = f"📋 <b>API Configurations{env_text}</b>\n\n"
        
        if not api_configs:
            text += "📝 No API configurations found"
            if environment:
                text += f" for {environment} environment"
            text += ".\n\n"
            text += "Use the 'Create New API' button to add your first API configuration."
            return text
        
        # Group by category
        categories = {}
        for config in api_configs:
            category = config.get("category", "general")
            if category not in categories:
                categories[category] = []
            categories[category].append(config)
        
        for category, configs in categories.items():
            text += f"📁 <b>{category.title()}</b>\n"
            
            for config in configs:
                # Status indicators
                status_icon = "🟢" if config.get("enabled") else "🔴"
                health_icon = {
                    "healthy": "✅",
                    "unhealthy": "❌",
                    "unknown": "❓"
                }.get(config.get("health_status", "unknown"), "❓")
                
                # Environment badge
                env_badge = {
                    "development": "🧪",
                    "staging": "🔄",
                    "production": "🚀"
                }.get(config.get("environment", "development"), "🧪")
                
                text += f"{status_icon}{health_icon}{env_badge} <b>{config['display_name']}</b>\n"
                text += f"   <code>{config['base_url']}</code>\n"
                
                # Additional info
                endpoints_count = config.get("endpoints_count", 0)
                auth_type = config.get("auth_type", "none")
                text += f"   📡 {endpoints_count} endpoints • 🔐 {auth_type}\n"
                
                if config.get("last_used"):
                    last_used = config["last_used"]
                    if isinstance(last_used, str):
                        text += f"   🕒 Last used: {last_used[:10]}\n"
                
                text += "\n"
        
        text += f"📊 Total: {len(api_configs)} APIs"
        
        return text
    
    def create_api_list_keyboard(
        self,
        api_configs: List[Dict[str, Any]],
        environment: Optional[str] = None
    ) -> InlineKeyboardMarkup:
        """Create keyboard for API list"""
        keyboard = []
        
        # Add individual API buttons (first 8 to avoid message limits)
        for config in api_configs[:8]:
            status_icon = "🟢" if config.get("enabled") else "🔴"
            health_icon = {
                "healthy": "✅",
                "unhealthy": "❌",
                "unknown": "❓"
            }.get(config.get("health_status", "unknown"), "❓")
            
            button_text = f"{status_icon}{health_icon} {config['display_name']}"
            keyboard.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"api_view:{config['name']}"
                )
            ])
        
        # Action buttons
        keyboard.extend([
            [
                InlineKeyboardButton(text="➕ Create New", callback_data="api_create"),
                InlineKeyboardButton(text="🔍 Filter & Search", callback_data="api_filter")
            ],
            [
                InlineKeyboardButton(text="🔄 Refresh", callback_data=f"api_list:{environment}" if environment else "api_list"),
                InlineKeyboardButton(text="🔍 Health Check All", callback_data="api_health_check_all")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back", callback_data="api_main")
            ]
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard)
    
    def format_create_api_step1(self) -> str:
        """Format step 1 of API creation"""
        return (
            "🆕 <b>Create New API Configuration</b>\n\n"
            "📝 <b>Step 1/4: API Name</b>\n\n"
            "Please enter a unique name for your API configuration.\n\n"
            "📋 <b>Requirements:</b>\n"
            "• 2-50 characters\n"
            "• Letters, numbers, hyphens, underscores only\n"
            "• Must be unique\n\n"
            "💡 <b>Examples:</b> <code>payment-api</code>, <code>user_service</code>, <code>inventory-v2</code>\n\n"
            "✏️ Type the API name:"
        )
    
    def format_create_api_step2(self, api_name: str) -> str:
        """Format step 2 of API creation"""
        return (
            f"🆕 <b>Create API: {api_name}</b>\n\n"
            "🌐 <b>Step 2/4: Base URL</b>\n\n"
            "Please enter the base URL for your API.\n\n"
            "📋 <b>Requirements:</b>\n"
            "• Must start with http:// or https://\n"
            "• Should not end with a slash\n"
            "• Must be a valid URL\n\n"
            "💡 <b>Examples:</b>\n"
            "• <code>https://api.example.com</code>\n"
            "• <code>https://api.example.com/v1</code>\n"
            "• <code>http://localhost:8080</code>\n\n"
            "✏️ Type the base URL:"
        )
    
    def format_create_api_step3(self, api_name: str, base_url: str) -> str:
        """Format step 3 of API creation"""
        return (
            f"🆕 <b>Create API: {api_name}</b>\n\n"
            f"🌐 <b>Base URL:</b> <code>{base_url}</code>\n\n"
            "🔐 <b>Step 3/4: Authentication</b>\n\n"
            "Choose the authentication method for your API:\n\n"
            "🔓 <b>None</b> - No authentication required\n"
            "🔑 <b>API Key</b> - API key in header\n"
            "🎫 <b>Bearer Token</b> - JWT or bearer token\n"
            "👤 <b>Basic Auth</b> - Username and password\n"
            "🔧 <b>Custom Headers</b> - Custom authentication headers\n\n"
            "Choose an authentication type:"
        )
    
    def create_auth_type_keyboard(self) -> InlineKeyboardMarkup:
        """Create authentication type selection keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔓 None", callback_data="auth_type:none"),
                InlineKeyboardButton(text="🔑 API Key", callback_data="auth_type:api_key")
            ],
            [
                InlineKeyboardButton(text="🎫 Bearer Token", callback_data="auth_type:bearer_token"),
                InlineKeyboardButton(text="👤 Basic Auth", callback_data="auth_type:basic_auth")
            ],
            [
                InlineKeyboardButton(text="🔧 Custom Headers", callback_data="auth_type:custom_header")
            ],
            [
                InlineKeyboardButton(text="❌ Cancel", callback_data="cancel")
            ]
        ])
    
    def format_auth_data_input(self, auth_type: str) -> str:
        """Format authentication data input prompt"""
        prompts = {
            "api_key": (
                "🔑 <b>API Key Authentication</b>\n\n"
                "Please provide your API key details in JSON format:\n\n"
                "📋 <b>Format:</b>\n"
                "<code>{\n"
                '  "api_key": "your-api-key-here",\n'
                '  "api_key_header": "X-API-Key"\n'
                "}</code>\n\n"
                "💡 The header name is optional (defaults to X-API-Key)\n\n"
                "✏️ Type the JSON configuration:"
            ),
            "bearer_token": (
                "🎫 <b>Bearer Token Authentication</b>\n\n"
                "Please provide your bearer token in JSON format:\n\n"
                "📋 <b>Format:</b>\n"
                "<code>{\n"
                '  "bearer_token": "your-token-here"\n'
                "}</code>\n\n"
                "✏️ Type the JSON configuration:"
            ),
            "basic_auth": (
                "👤 <b>Basic Authentication</b>\n\n"
                "Please provide username and password in JSON format:\n\n"
                "📋 <b>Format:</b>\n"
                "<code>{\n"
                '  "username": "your-username",\n'
                '  "password": "your-password"\n'
                "}</code>\n\n"
                "✏️ Type the JSON configuration:"
            ),
            "custom_header": (
                "🔧 <b>Custom Headers Authentication</b>\n\n"
                "Please provide custom headers in JSON format:\n\n"
                "📋 <b>Format:</b>\n"
                "<code>{\n"
                '  "custom_headers": {\n'
                '    "X-Custom-Auth": "value1",\n'
                '    "X-Client-ID": "value2"\n'
                "  }\n"
                "}</code>\n\n"
                "✏️ Type the JSON configuration:"
            )
        }
        
        return prompts.get(auth_type, "Please provide authentication configuration:")
    
    def format_create_api_step4(self, api_name: str) -> str:
        """Format step 4 of API creation"""
        return (
            f"🆕 <b>Create API: {api_name}</b>\n\n"
            "📡 <b>Step 4/4: Endpoints (Optional)</b>\n\n"
            "You can add endpoints now or skip this step and add them later.\n\n"
            "🎯 <b>Options:</b>\n"
            "• <b>Skip</b> - Create API without endpoints\n"
            "• <b>Add Basic</b> - Add common CRUD endpoints\n"
            "• <b>Custom</b> - Define your own endpoints\n\n"
            "💡 You can always add or modify endpoints later through the API details page.\n\n"
            "Choose an option:"
        )
    
    def create_endpoints_keyboard(self) -> InlineKeyboardMarkup:
        """Create endpoints configuration keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="⏭️ Skip Endpoints", callback_data="skip_endpoints"),
                InlineKeyboardButton(text="📋 Add Basic CRUD", callback_data="add_basic_endpoints")
            ],
            [
                InlineKeyboardButton(text="🔧 Custom Endpoints", callback_data="custom_endpoints")
            ],
            [
                InlineKeyboardButton(text="❌ Cancel", callback_data="cancel")
            ]
        ])
    
    def format_creation_success(self, api_name: str, message: str) -> str:
        """Format successful API creation message"""
        return (
            f"✅ <b>API Created Successfully!</b>\n\n"
            f"🎉 <b>{api_name}</b> has been created and registered.\n\n"
            f"📝 <b>Details:</b>\n{message}\n\n"
            "🚀 <b>Next Steps:</b>\n"
            "• Test the API connection\n"
            "• Add more endpoints if needed\n"
            "• Configure additional settings\n"
            "• Start using the API in your applications\n\n"
            "What would you like to do next?"
        )
    
    def create_post_creation_keyboard(self, api_name: str) -> InlineKeyboardMarkup:
        """Create post-creation action keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🧪 Test API", callback_data=f"api_test:{api_name}"),
                InlineKeyboardButton(text="👁️ View Details", callback_data=f"api_view:{api_name}")
            ],
            [
                InlineKeyboardButton(text="➕ Create Another", callback_data="api_create"),
                InlineKeyboardButton(text="📋 List All APIs", callback_data="api_list")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Main", callback_data="api_main")
            ]
        ])
    
    def format_creation_error(self, error_message: str) -> str:
        """Format API creation error message"""
        return (
            "❌ <b>API Creation Failed</b>\n\n"
            f"🚫 <b>Error:</b> {error_message}\n\n"
            "💡 <b>Common Issues:</b>\n"
            "• Invalid URL format\n"
            "• Duplicate API name\n"
            "• Invalid authentication configuration\n"
            "• Network connectivity issues\n\n"
            "Please check your configuration and try again."
        )
    
    def create_retry_keyboard(self) -> InlineKeyboardMarkup:
        """Create retry action keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔄 Try Again", callback_data="api_create"),
                InlineKeyboardButton(text="📋 List APIs", callback_data="api_list")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Main", callback_data="api_main")
            ]
        ])
    
    def create_cancel_keyboard(self) -> InlineKeyboardMarkup:
        """Create cancel operation keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="❌ Cancel", callback_data="cancel")
            ]
        ])
    
    def format_api_details(self, api_details: Dict[str, Any]) -> str:
        """Format detailed API information"""
        admin_info = api_details["admin_info"]
        shared_config = api_details["shared_config"]
        endpoints = api_details["endpoints"]
        auth_info = api_details["authentication"]
        
        # Header
        text = f"🔍 <b>API Details: {admin_info['display_name']}</b>\n\n"
        
        # Basic info
        text += f"📋 <b>Basic Information:</b>\n"
        text += f"• Name: <code>{admin_info['name']}</code>\n"
        text += f"• Base URL: <code>{admin_info['base_url']}</code>\n"
        text += f"• Environment: {admin_info['environment']}\n"
        text += f"• Status: {admin_info['status']}\n"
        
        # Status indicators
        status_icon = "🟢" if admin_info.get("enabled") else "🔴"
        health_icon = {
            "healthy": "✅",
            "unhealthy": "❌",
            "unknown": "❓"
        }.get(admin_info.get("health_status", "unknown"), "❓")
        
        text += f"• Enabled: {status_icon}\n"
        text += f"• Health: {health_icon} {admin_info.get('health_status', 'unknown')}\n\n"
        
        # Authentication
        text += f"🔐 <b>Authentication:</b>\n"
        text += f"• Type: {auth_info['type']}\n"
        text += f"• Configured: {'✅' if auth_info['has_credentials'] else '❌'}\n\n"
        
        # Endpoints
        text += f"📡 <b>Endpoints ({len(endpoints)}):</b>\n"
        if endpoints:
            for name, ep in list(endpoints.items())[:5]:  # Show first 5
                text += f"• <code>{ep['method']}</code> {ep['path']} - {ep.get('description', name)}\n"
            if len(endpoints) > 5:
                text += f"• ... and {len(endpoints) - 5} more\n"
        else:
            text += "• No endpoints configured\n"
        
        text += "\n"
        
        # Usage stats
        if admin_info.get("request_count", 0) > 0:
            text += f"📊 <b>Usage:</b>\n"
            text += f"• Requests: {admin_info['request_count']}\n"
            if admin_info.get("last_used"):
                text += f"• Last used: {admin_info['last_used']}\n"
            text += "\n"
        
        # Description
        if admin_info.get("description"):
            text += f"📝 <b>Description:</b>\n{admin_info['description']}\n\n"
        
        return text
    
    def create_api_details_keyboard(self, api_name: str) -> InlineKeyboardMarkup:
        """Create API details action keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🧪 Test API", callback_data=f"api_test:{api_name}"),
                InlineKeyboardButton(text="✏️ Edit", callback_data=f"api_edit:{api_name}")
            ],
            [
                InlineKeyboardButton(text="📡 Test Endpoints", callback_data=f"api_test_endpoints:{api_name}"),
                InlineKeyboardButton(text="⚙️ Settings", callback_data=f"api_settings:{api_name}")
            ],
            [
                InlineKeyboardButton(text="📋 Audit Log", callback_data=f"api_audit:{api_name}"),
                InlineKeyboardButton(text="📤 Export", callback_data=f"api_export:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to List", callback_data="api_list")
            ]
        ])
    
    def format_test_results(
        self,
        api_name: str,
        success: bool,
        message: str,
        test_results: Optional[Dict[str, Any]]
    ) -> str:
        """Format API test results"""
        status_icon = "✅" if success else "❌"
        
        text = f"🧪 <b>API Test Results: {api_name}</b>\n\n"
        text += f"{status_icon} <b>Status:</b> {self._escape(message)}\n\n"
        
        if test_results and "tests" in test_results:
            text += f"📊 <b>Test Details:</b>\n"
            for test in test_results["tests"]:
                test_icon = "✅" if test.get("success") else "❌"
                test_type = test.get("type", test.get("endpoint", "unknown"))
                text += f"{test_icon} {self._escape(test_type)}"
                
                if "response_time_ms" in test:
                    text += f" ({test['response_time_ms']:.1f}ms)"
                
                if "error" in test:
                    text += f"\n   Error: {self._escape(test['error'])}"
                
                text += "\n"
        
        if test_results and "test_time" in test_results:
            text += f"\n🕒 <b>Test Time:</b> {test_results['test_time']}"
        
        return text
    
    def create_test_results_keyboard(self, api_name: str, success: bool) -> InlineKeyboardMarkup:
        """Create test results action keyboard"""
        keyboard = [
            [
                InlineKeyboardButton(text="🔄 Test Again", callback_data=f"api_test:{api_name}"),
                InlineKeyboardButton(text="👁️ View Details", callback_data=f"api_view:{api_name}")
            ]
        ]
        
        if success:
            keyboard.append([
                InlineKeyboardButton(text="📡 Test Endpoints", callback_data=f"api_test_endpoints:{api_name}")
            ])
        
        keyboard.append([
            InlineKeyboardButton(text="⬅️ Back to List", callback_data="api_list")
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=keyboard)
    
    def format_endpoint_test_results(
        self,
        api_name: str,
        endpoint_name: str,
        success: bool,
        message: str,
        test_results: Optional[Dict[str, Any]]
    ) -> str:
        """Format endpoint-specific test results"""
        status_icon = "✅" if success else "❌"
        
        text = f"🧪 <b>Endpoint Test: {api_name}/{endpoint_name}</b>\n\n"
        text += f"{status_icon} <b>Result:</b> {self._escape(message)}\n\n"
        
        if test_results and "tests" in test_results:
            for test in test_results["tests"]:
                if test.get("endpoint") == endpoint_name:
                    if "response_time_ms" in test:
                        text += f"⏱️ <b>Response Time:</b> {test['response_time_ms']:.1f}ms\n"
                    
                    if "error" in test:
                        text += f"🚫 <b>Error:</b> {self._escape(test['error'])}\n"
                    
                    break
        
        return text
    
    def format_health_check_results(self, health_results: List[Dict[str, Any]]) -> str:
        """Format health check results for all APIs"""
        text = "🔍 <b>API Health Check Results</b>\n\n"
        
        healthy_count = sum(1 for result in health_results if result["success"])
        total_count = len(health_results)
        
        text += f"📊 <b>Summary:</b> {healthy_count}/{total_count} APIs healthy\n\n"
        
        for result in health_results:
            status_icon = "✅" if result["success"] else "❌"
            text += f"{status_icon} <b>{result['name']}</b>\n"
            
            if result["success"] and result.get("test_results"):
                # Extract response time if available
                for test in result["test_results"].get("tests", []):
                    if "response_time_ms" in test:
                        text += f"   ⏱️ {test['response_time_ms']:.1f}ms\n"
                        break
            else:
                text += f"   🚫 {result['message']}\n"
            
            text += "\n"
        
        return text
    
    def create_health_check_keyboard(self) -> InlineKeyboardMarkup:
        """Create health check results keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔄 Check Again", callback_data="api_health_check_all"),
                InlineKeyboardButton(text="📋 List APIs", callback_data="api_list")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Main", callback_data="api_main")
            ]
        ])

    # New UI methods for missing functionality

    def format_api_edit_menu(self, api_details: Dict[str, Any]) -> str:
        """Format API edit menu"""
        admin_info = api_details["admin_info"]

        text = f"✏️ <b>Edit API: {admin_info['display_name']}</b>\n\n"
        text += f"📋 <b>Current Configuration:</b>\n"
        text += f"• Name: <code>{admin_info['name']}</code>\n"
        text += f"• Base URL: <code>{admin_info['base_url']}</code>\n"
        text += f"• Environment: {admin_info['environment']}\n"
        text += f"• Status: {admin_info['status']}\n"
        text += f"• Category: {admin_info.get('category', 'general')}\n\n"
        text += "🔧 <b>What would you like to edit?</b>"

        return text

    def create_api_edit_keyboard(self, api_name: str) -> InlineKeyboardMarkup:
        """Create API edit options keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🌐 Base URL", callback_data=f"edit_base_url:{api_name}"),
                InlineKeyboardButton(text="🔐 Authentication", callback_data=f"edit_auth:{api_name}")
            ],
            [
                InlineKeyboardButton(text="📡 Endpoints", callback_data=f"edit_endpoints:{api_name}"),
                InlineKeyboardButton(text="⚙️ Settings", callback_data=f"edit_settings:{api_name}")
            ],
            [
                InlineKeyboardButton(text="📝 Description", callback_data=f"edit_description:{api_name}"),
                InlineKeyboardButton(text="🏷️ Category", callback_data=f"edit_category:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Details", callback_data=f"api_view:{api_name}")
            ]
        ])

    def format_api_settings(self, api_details: Dict[str, Any]) -> str:
        """Format API settings display"""
        admin_info = api_details["admin_info"]
        shared_config = api_details["shared_config"]

        text = f"⚙️ <b>API Settings: {admin_info['display_name']}</b>\n\n"

        # Basic settings
        text += f"📋 <b>Basic Settings:</b>\n"
        text += f"• Enabled: {'✅ Yes' if admin_info.get('enabled', True) else '❌ No'}\n"
        text += f"• Environment: {admin_info['environment']}\n"
        text += f"• Category: {admin_info.get('category', 'general')}\n\n"

        # Timeout settings
        timeout_config = shared_config.get("timeout", {})
        text += f"⏱️ <b>Timeout Settings:</b>\n"
        text += f"• Connect: {timeout_config.get('connect', 10)}s\n"
        text += f"• Read: {timeout_config.get('read', 30)}s\n"
        text += f"• Total: {timeout_config.get('total', 60)}s\n\n"

        # Retry settings
        retry_config = shared_config.get("retry", {})
        text += f"🔄 <b>Retry Settings:</b>\n"
        text += f"• Max Attempts: {retry_config.get('max_attempts', 3)}\n"
        text += f"• Delay: {retry_config.get('delay', 1.0)}s\n"
        text += f"• Backoff Factor: {retry_config.get('backoff_factor', 2.0)}\n"

        return text

    def create_api_settings_keyboard(self, api_name: str) -> InlineKeyboardMarkup:
        """Create API settings keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔄 Toggle Enable/Disable", callback_data=f"toggle_enabled:{api_name}"),
                InlineKeyboardButton(text="⏱️ Edit Timeouts", callback_data=f"edit_timeouts:{api_name}")
            ],
            [
                InlineKeyboardButton(text="🔄 Edit Retry Settings", callback_data=f"edit_retry:{api_name}"),
                InlineKeyboardButton(text="🏷️ Change Category", callback_data=f"edit_category:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Details", callback_data=f"api_view:{api_name}")
            ]
        ])

    def format_api_audit_log(self, api_name: str, audit_logs: List[Dict[str, Any]]) -> str:
        """Format API audit log display"""
        text = f"📋 <b>Audit Log: {api_name}</b>\n\n"

        if not audit_logs:
            text += "📝 No audit entries found.\n\n"
            text += "Audit entries are created when:\n"
            text += "• API configuration is created or updated\n"
            text += "• Settings are changed\n"
            text += "• API is enabled/disabled\n"
            return text

        text += f"📊 <b>Recent Activity ({len(audit_logs)} entries):</b>\n\n"

        for entry in audit_logs[-10:]:  # Show last 10 entries
            timestamp = entry.get("timestamp", "Unknown")
            action = entry.get("action", "Unknown action")
            user = entry.get("user", "Unknown user")
            details = entry.get("details", "")

            # Format timestamp
            if timestamp and timestamp != "Unknown":
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    formatted_time = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    formatted_time = timestamp[:16]  # Fallback
            else:
                formatted_time = "Unknown time"

            text += f"🕒 <b>{formatted_time}</b>\n"
            text += f"👤 User: {user}\n"
            text += f"🔧 Action: {action}\n"
            if details:
                text += f"📝 Details: {details}\n"
            text += "\n"

        return text

    def create_api_audit_keyboard(self, api_name: str) -> InlineKeyboardMarkup:
        """Create API audit log keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔄 Refresh", callback_data=f"api_audit:{api_name}"),
                InlineKeyboardButton(text="📤 Export Log", callback_data=f"export_audit:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Details", callback_data=f"api_view:{api_name}")
            ]
        ])

    def format_api_export(self, api_details: Dict[str, Any]) -> str:
        """Format API export options"""
        admin_info = api_details["admin_info"]

        text = f"📤 <b>Export API: {admin_info['display_name']}</b>\n\n"
        text += f"📋 <b>Export Options:</b>\n"
        text += f"• Configuration only (JSON format)\n"
        text += f"• Configuration with audit log\n"
        text += f"• Shareable configuration template\n\n"
        text += f"🔧 <b>API Details:</b>\n"
        text += f"• Name: <code>{admin_info['name']}</code>\n"
        text += f"• Environment: {admin_info['environment']}\n"
        text += f"• Endpoints: {len(api_details.get('endpoints', {}))}\n"
        text += f"• Created: {admin_info.get('created_at', 'Unknown')[:10]}\n\n"
        text += "📝 Choose export format:"

        return text

    def create_api_export_keyboard(self, api_name: str) -> InlineKeyboardMarkup:
        """Create API export options keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="📄 Config Only", callback_data=f"export_config:{api_name}"),
                InlineKeyboardButton(text="📋 Config + Audit", callback_data=f"export_full:{api_name}")
            ],
            [
                InlineKeyboardButton(text="📝 Template", callback_data=f"export_template:{api_name}"),
                InlineKeyboardButton(text="💾 Download All", callback_data=f"export_all:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Details", callback_data=f"api_view:{api_name}")
            ]
        ])

    def format_endpoint_test_menu(self, api_details: Dict[str, Any]) -> str:
        """Format endpoint testing menu"""
        admin_info = api_details["admin_info"]
        endpoints = api_details.get("endpoints", {})

        text = f"📡 <b>Test Endpoints: {admin_info['display_name']}</b>\n\n"

        if not endpoints:
            text += "📝 No endpoints configured for this API.\n\n"
            text += "Add endpoints in the API configuration to enable endpoint testing."
            return text

        text += f"🔗 <b>Available Endpoints ({len(endpoints)}):</b>\n\n"

        for name, endpoint in endpoints.items():
            method = endpoint.get("method", "GET")
            path = endpoint.get("path", "/")
            description = endpoint.get("description", "No description")

            method_icon = {
                "GET": "📥",
                "POST": "📤",
                "PUT": "🔄",
                "DELETE": "🗑️",
                "PATCH": "✏️"
            }.get(method, "📡")

            text += f"{method_icon} <b>{name}</b> ({method})\n"
            text += f"   <code>{path}</code>\n"
            text += f"   {description}\n\n"

        text += "🧪 Select an endpoint to test:"

        return text

    def create_endpoint_test_keyboard(self, api_name: str, endpoints: Dict[str, Any]) -> InlineKeyboardMarkup:
        """Create endpoint testing keyboard"""
        keyboard = []

        # Add endpoint buttons (max 2 per row)
        endpoint_buttons = []
        for name, endpoint in endpoints.items():
            method = endpoint.get("method", "GET")
            button_text = f"{method} {name}"
            endpoint_buttons.append(
                InlineKeyboardButton(text=button_text, callback_data=f"api_test_endpoint:{api_name}:{name}")
            )

        # Arrange in rows of 2
        for i in range(0, len(endpoint_buttons), 2):
            row = endpoint_buttons[i:i+2]
            keyboard.append(row)

        # Add control buttons
        keyboard.extend([
            [
                InlineKeyboardButton(text="🧪 Test All", callback_data=f"api_test:{api_name}"),
                InlineKeyboardButton(text="🔄 Refresh", callback_data=f"api_test_endpoints:{api_name}")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Details", callback_data=f"api_view:{api_name}")
            ]
        ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard)

    # Filtering and Search UI Methods

    def format_filter_menu(self) -> str:
        """Format filtering options menu"""
        text = "🔍 <b>Filter & Search APIs</b>\n\n"
        text += "📋 <b>Filter Options:</b>\n"
        text += "• Filter by status (enabled/disabled)\n"
        text += "• Filter by category\n"
        text += "• Filter by environment\n"
        text += "• Search by name or description\n\n"
        text += "🎯 Choose a filter option:"

        return text

    def create_filter_keyboard(self) -> InlineKeyboardMarkup:
        """Create filtering options keyboard"""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(text="🔍 Search APIs", callback_data="api_search"),
                InlineKeyboardButton(text="✅ Filter by Status", callback_data="filter_status_menu")
            ],
            [
                InlineKeyboardButton(text="📁 Filter by Category", callback_data="filter_category_menu"),
                InlineKeyboardButton(text="🌍 Filter by Environment", callback_data="filter_env_menu")
            ],
            [
                InlineKeyboardButton(text="🗑️ Clear All Filters", callback_data="clear_filters"),
                InlineKeyboardButton(text="⬅️ Back to List", callback_data="api_list")
            ]
        ])

    def format_search_prompt(self) -> str:
        """Format search input prompt"""
        text = "🔍 <b>Search APIs</b>\n\n"
        text += "📝 Enter search term to find APIs by:\n"
        text += "• API name\n"
        text += "• Display name\n"
        text += "• Description\n"
        text += "• Base URL\n\n"
        text += "💡 <b>Examples:</b>\n"
        text += "• <code>weather</code> - Find weather APIs\n"
        text += "• <code>api.example.com</code> - Find by URL\n"
        text += "• <code>payment</code> - Find payment-related APIs\n\n"
        text += "✏️ Type your search term:"

        return text

    def format_search_results(self, search_term: str, api_configs: List[Dict[str, Any]]) -> str:
        """Format search results"""
        text = f"🔍 <b>Search Results for: '{search_term}'</b>\n\n"

        if not api_configs:
            text += "📝 No APIs found matching your search.\n\n"
            text += "💡 <b>Tips:</b>\n"
            text += "• Try different keywords\n"
            text += "• Check spelling\n"
            text += "• Use partial matches\n"
            return text

        text += f"📊 Found {len(api_configs)} matching API(s):\n\n"

        for config in api_configs:
            status_icon = "🟢" if config.get("enabled", True) else "🔴"
            health_icon = {
                "healthy": "✅",
                "unhealthy": "❌",
                "unknown": "❓"
            }.get(config.get("health_status", "unknown"), "❓")

            text += f"{status_icon}{health_icon} <b>{config['display_name']}</b>\n"
            text += f"   <code>{config['base_url']}</code>\n"
            text += f"   📁 {config.get('category', 'general')} • 🌍 {config['environment']}\n\n"

        return text

    def create_search_results_keyboard(self, api_configs: List[Dict[str, Any]], search_term: str) -> InlineKeyboardMarkup:
        """Create search results keyboard"""
        keyboard = []

        # Add API buttons (max 2 per row)
        api_buttons = []
        for config in api_configs[:10]:  # Limit to first 10 results
            button_text = f"📋 {config['display_name'][:20]}"
            if len(config['display_name']) > 20:
                button_text += "..."
            api_buttons.append(
                InlineKeyboardButton(text=button_text, callback_data=f"api_view:{config['name']}")
            )

        # Arrange in rows of 2
        for i in range(0, len(api_buttons), 2):
            row = api_buttons[i:i+2]
            keyboard.append(row)

        # Add control buttons
        keyboard.extend([
            [
                InlineKeyboardButton(text="🔍 New Search", callback_data="api_search"),
                InlineKeyboardButton(text="📋 All APIs", callback_data="api_list")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to Main", callback_data="api_main")
            ]
        ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard)

    def format_filtered_api_list(self, api_configs: List[Dict[str, Any]], filter_type: str, filter_value: str) -> str:
        """Format filtered API list"""
        filter_display = {
            "status": f"Status: {filter_value}",
            "category": f"Category: {filter_value}",
            "environment": f"Environment: {filter_value}"
        }.get(filter_type, f"{filter_type}: {filter_value}")

        text = f"🔍 <b>Filtered APIs - {filter_display}</b>\n\n"

        if not api_configs:
            text += f"📝 No APIs found with {filter_display.lower()}.\n\n"
            text += "Try a different filter or clear filters to see all APIs."
            return text

        text += f"📊 Found {len(api_configs)} API(s):\n\n"

        # Group by category for better organization
        categories = {}
        for config in api_configs:
            category = config.get("category", "general")
            if category not in categories:
                categories[category] = []
            categories[category].append(config)

        for category, configs in categories.items():
            if len(categories) > 1:  # Only show category headers if multiple categories
                text += f"📁 <b>{category.title()}</b>\n"

            for config in configs:
                status_icon = "🟢" if config.get("enabled", True) else "🔴"
                health_icon = {
                    "healthy": "✅",
                    "unhealthy": "❌",
                    "unknown": "❓"
                }.get(config.get("health_status", "unknown"), "❓")

                env_badge = {
                    "development": "🔧",
                    "staging": "🧪",
                    "production": "🚀"
                }.get(config["environment"], "🔧")

                text += f"{status_icon}{health_icon}{env_badge} <b>{config['display_name']}</b>\n"
                text += f"   <code>{config['base_url']}</code>\n\n"

        return text

    def create_filtered_list_keyboard(self, api_configs: List[Dict[str, Any]], filter_type: str, filter_value: str) -> InlineKeyboardMarkup:
        """Create filtered list keyboard"""
        keyboard = []

        # Add API buttons (max 2 per row)
        api_buttons = []
        for config in api_configs[:8]:  # Limit to first 8 results
            button_text = f"📋 {config['display_name'][:15]}"
            if len(config['display_name']) > 15:
                button_text += "..."
            api_buttons.append(
                InlineKeyboardButton(text=button_text, callback_data=f"api_view:{config['name']}")
            )

        # Arrange in rows of 2
        for i in range(0, len(api_buttons), 2):
            row = api_buttons[i:i+2]
            keyboard.append(row)

        # Add control buttons
        keyboard.extend([
            [
                InlineKeyboardButton(text="🔍 Change Filter", callback_data="api_filter"),
                InlineKeyboardButton(text="🗑️ Clear Filter", callback_data="clear_filters")
            ],
            [
                InlineKeyboardButton(text="⬅️ Back to List", callback_data="api_list")
            ]
        ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard)
