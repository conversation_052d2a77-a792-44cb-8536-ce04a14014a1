"""
Shared API Constants

Defines constants used throughout the shared API system.
"""

from enum import Enum
from typing import Set

# Default configuration values
DEFAULT_TIMEOUT = 30
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 1.0
DEFAULT_CACHE_TTL = 300  # 5 minutes

# HTTP configuration
DEFAULT_CONNECT_TIMEOUT = 10
DEFAULT_READ_TIMEOUT = 30
DEFAULT_TOTAL_TIMEOUT = 60

# Rate limiting defaults
DEFAULT_RATE_LIMIT_PER_MINUTE = 60
DEFAULT_RATE_LIMIT_PER_HOUR = 1000
DEFAULT_BURST_LIMIT = 10

# Retry configuration
DEFAULT_BACKOFF_FACTOR = 1.0
DEFAULT_RETRY_STATUS_CODES = {500, 502, 503, 504}
MAX_RETRY_ATTEMPTS = 10

# Authentication
DEFAULT_API_KEY_HEADER = "X-API-Key"
DEFAULT_AUTH_HEADER = "Authorization"

# Logging
DEFAULT_LOG_LEVEL = "INFO"
MAX_LOG_RESPONSE_SIZE = 1000  # characters


class HTTPMethod(str, Enum):
    """Supported HTTP methods"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    PATCH = "PATCH"
    DELETE = "DELETE"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class AuthenticationType(str, Enum):
    """Supported authentication types"""
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH2 = "oauth2"
    CUSTOM_HEADER = "custom_header"


class APIEnvironment(str, Enum):
    """API environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class APIStatus(str, Enum):
    """API status indicators"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


# Derived constants
SUPPORTED_HTTP_METHODS: Set[str] = {method.value for method in HTTPMethod}
SUPPORTED_AUTH_TYPES: Set[str] = {auth_type.value for auth_type in AuthenticationType}
SUPPORTED_ENVIRONMENTS: Set[str] = {env.value for env in APIEnvironment}
SUPPORTED_STATUSES: Set[str] = {status.value for status in APIStatus}

# Content types
CONTENT_TYPE_JSON = "application/json"
CONTENT_TYPE_FORM = "application/x-www-form-urlencoded"
CONTENT_TYPE_TEXT = "text/plain"

# Common headers
COMMON_HEADERS = {
    "User-Agent": "SharedAPIClient/1.0.0",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9",
    "Content-Type": CONTENT_TYPE_JSON,
}

# Error messages
ERROR_MESSAGES = {
    "INVALID_CONFIG": "Invalid API configuration provided",
    "MISSING_AUTH": "Authentication configuration is required but not provided",
    "INVALID_URL": "Invalid URL format",
    "TIMEOUT": "Request timed out",
    "RATE_LIMIT": "Rate limit exceeded",
    "NETWORK_ERROR": "Network connectivity error",
    "INVALID_RESPONSE": "Invalid response format",
    "AUTH_FAILED": "Authentication failed",
}

# Configuration validation patterns
URL_PATTERN = r"^https?://[^\s/$.?#].[^\s]*$"
EMAIL_PATTERN = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
