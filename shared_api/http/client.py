"""
Configurable HTTP Client

A flexible HTTP client that can be configured with different API settings,
authentication methods, retry logic, and error handling strategies.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass

import aiohttp
from aiohttp import ClientTimeout, ClientSession, ClientError

from ..core.interfaces import BaseAPIClient, APIConfigProtocol
from ..core.constants import HTTPMethod, DEFAULT_TIMEOUT
from ..core.exceptions import (
    HTTPClientError,
    TimeoutError,
    NetworkError,
    RateLimitError,
    AuthenticationError,
)
from ..config.api_config import APIConfiguration, TimeoutConfiguration, RetryConfiguration

logger = logging.getLogger(__name__)


@dataclass
class HTTPResponse:
    """HTTP response wrapper"""
    status_code: int
    headers: Dict[str, str]
    text: str
    json_data: Optional[Dict[str, Any]] = None
    success: bool = True
    error_message: Optional[str] = None
    response_time_ms: Optional[float] = None


class ConfigurableHTTPClient(BaseAPIClient):
    """
    Configurable HTTP client for API requests
    
    Features:
    - Configurable timeouts, retries, and authentication
    - Comprehensive error handling and logging
    - Support for multiple authentication types
    - Automatic retry with exponential backoff
    - Request/response middleware support
    """
    
    def __init__(self, config: APIConfiguration):
        super().__init__(config)
        self.config = config
        self._session: Optional[ClientSession] = None
        self.logger = logging.getLogger(f"{__name__}.{config.name}")
    
    async def _ensure_session(self) -> ClientSession:
        """Ensure HTTP session is available"""
        if self._session is None or self._session.closed:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            
            timeout = ClientTimeout(
                total=self.config.timeout_config.total,
                connect=self.config.timeout_config.connect,
                sock_read=self.config.timeout_config.read,
            )
            
            self._session = ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self.config.default_headers,
            )
        
        return self._session
    
    async def _make_request(
        self,
        method: HTTPMethod,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Make HTTP request with retry logic and error handling"""
        session = await self._ensure_session()
        retry_config = self.config.retry_config
        
        last_exception = None
        
        for attempt in range(retry_config.max_attempts + 1):
            start_time = time.time()
            
            try:
                self.logger.debug(
                    f"Making {method.value} request to {url} (attempt {attempt + 1})"
                )
                
                # Prepare request arguments
                request_kwargs = {
                    "headers": headers,
                    "params": params,
                    **kwargs
                }
                
                if data is not None:
                    if headers.get("Content-Type", "").startswith("application/json"):
                        request_kwargs["json"] = data
                    else:
                        request_kwargs["data"] = data
                
                # Make the request
                async with session.request(method.value, url, **request_kwargs) as response:
                    response_time = (time.time() - start_time) * 1000
                    response_text = await response.text()
                    
                    # Parse JSON response if possible
                    json_data = None
                    if response.content_type == "application/json":
                        try:
                            json_data = await response.json()
                        except json.JSONDecodeError:
                            self.logger.warning(f"Failed to parse JSON response: {response_text[:200]}")
                    
                    # Create response object
                    http_response = HTTPResponse(
                        status_code=response.status,
                        headers=dict(response.headers),
                        text=response_text,
                        json_data=json_data,
                        response_time_ms=response_time,
                    )
                    
                    # Check for success
                    if response.status < 400:
                        self.logger.debug(
                            f"Request successful: {response.status} in {response_time:.1f}ms"
                        )
                        return self._process_successful_response(http_response)
                    
                    # Handle error responses
                    if response.status == 401:
                        raise AuthenticationError(
                            f"Authentication failed: {response.status}",
                            details={"response": response_text, "status_code": response.status}
                        )
                    elif response.status == 429:
                        retry_after = response.headers.get("Retry-After")
                        raise RateLimitError(
                            f"Rate limit exceeded: {response.status}",
                            retry_after=int(retry_after) if retry_after else None,
                            details={"response": response_text, "status_code": response.status}
                        )
                    elif response.status in retry_config.retry_on_status:
                        # This is a retryable error
                        error_msg = f"HTTP {response.status}: {response_text[:200]}"
                        last_exception = HTTPClientError(
                            error_msg,
                            status_code=response.status,
                            response_text=response_text
                        )
                        
                        if attempt < retry_config.max_attempts:
                            delay = retry_config.delay * (retry_config.backoff_factor ** attempt)
                            self.logger.warning(
                                f"Request failed with {response.status}, retrying in {delay}s "
                                f"(attempt {attempt + 1}/{retry_config.max_attempts + 1})"
                            )
                            await asyncio.sleep(delay)
                            continue
                    else:
                        # Non-retryable error
                        raise HTTPClientError(
                            f"HTTP {response.status}: {response_text[:200]}",
                            status_code=response.status,
                            response_text=response_text
                        )
            
            except asyncio.TimeoutError as e:
                last_exception = TimeoutError(
                    f"Request timed out after {self.config.timeout_config.total}s",
                    timeout_duration=self.config.timeout_config.total
                )
                
                if attempt < retry_config.max_attempts:
                    delay = retry_config.delay * (retry_config.backoff_factor ** attempt)
                    self.logger.warning(
                        f"Request timed out, retrying in {delay}s "
                        f"(attempt {attempt + 1}/{retry_config.max_attempts + 1})"
                    )
                    await asyncio.sleep(delay)
                    continue
            
            except ClientError as e:
                last_exception = NetworkError(
                    f"Network error: {str(e)}",
                    original_error=e
                )
                
                if attempt < retry_config.max_attempts:
                    delay = retry_config.delay * (retry_config.backoff_factor ** attempt)
                    self.logger.warning(
                        f"Network error occurred, retrying in {delay}s "
                        f"(attempt {attempt + 1}/{retry_config.max_attempts + 1}): {e}"
                    )
                    await asyncio.sleep(delay)
                    continue
            
            except Exception as e:
                # Unexpected error, don't retry
                self.logger.error(f"Unexpected error during request: {e}", exc_info=True)
                raise HTTPClientError(f"Unexpected error: {str(e)}")
        
        # All retries exhausted
        if last_exception:
            self.logger.error(f"Request failed after {retry_config.max_attempts + 1} attempts")
            raise last_exception
        else:
            raise HTTPClientError("Request failed for unknown reason")
    
    def _process_successful_response(self, response: HTTPResponse) -> Dict[str, Any]:
        """Process a successful HTTP response"""
        if response.json_data is not None:
            return response.json_data
        
        # Try to parse as JSON if content type suggests it
        if response.headers.get("content-type", "").startswith("application/json"):
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                pass
        
        # Return text response wrapped in a dict
        return {
            "success": True,
            "data": response.text,
            "status_code": response.status_code,
            "headers": response.headers,
        }
    
    async def health_check(self) -> bool:
        """Check if the API is healthy"""
        try:
            # Try to find a health check endpoint
            health_endpoints = ["health", "status", "ping", "user_info"]
            
            for endpoint_name in health_endpoints:
                if endpoint_name in self.config.endpoints:
                    try:
                        await self.get(endpoint_name)
                        return True
                    except Exception as e:
                        self.logger.debug(f"Health check failed for {endpoint_name}: {e}")
                        continue
            
            # If no specific health endpoint, try the first available endpoint
            if self.config.endpoints:
                first_endpoint = next(iter(self.config.endpoints.keys()))
                try:
                    await self.get(first_endpoint)
                    return True
                except Exception as e:
                    self.logger.debug(f"Health check failed for {first_endpoint}: {e}")
            
            return False
        
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            return False
    
    async def close(self) -> None:
        """Close the HTTP client and clean up resources"""
        await super().close()
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
