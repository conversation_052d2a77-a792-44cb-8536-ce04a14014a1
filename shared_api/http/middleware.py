"""
Middleware for HTTP Requests and Responses

Provides middleware functionality for processing requests and responses
in the shared API system.
"""

import time
import logging
from typing import Dict, Any, Optional, Callable, Awaitable
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class RequestMiddleware(ABC):
    """
    Abstract base class for request middleware
    """
    
    @abstractmethod
    async def process_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process outgoing request
        
        Args:
            method: HTTP method
            url: Request URL
            headers: Request headers
            params: Query parameters
            data: Request body data
            **kwargs: Additional request arguments
            
        Returns:
            Processed request data
        """
        pass


class ResponseMiddleware(ABC):
    """
    Abstract base class for response middleware
    """
    
    @abstractmethod
    async def process_response(
        self,
        response: Dict[str, Any],
        request_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Process incoming response
        
        Args:
            response: Response data
            request_context: Context from the original request
            
        Returns:
            Processed response data
        """
        pass


class LoggingMiddleware(RequestMiddleware, ResponseMiddleware):
    """
    Middleware for logging requests and responses
    """
    
    def __init__(self, logger_name: Optional[str] = None):
        self.logger = logging.getLogger(logger_name or __name__)
    
    async def process_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Log outgoing request"""
        self.logger.debug(f"Request: {method} {url}")
        
        return {
            "method": method,
            "url": url,
            "headers": headers,
            "params": params,
            "data": data,
            "start_time": time.time(),
            **kwargs
        }
    
    async def process_response(
        self,
        response: Dict[str, Any],
        request_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Log incoming response"""
        start_time = request_context.get("start_time", time.time())
        duration = (time.time() - start_time) * 1000
        
        status_code = response.get("status_code", "unknown")
        self.logger.debug(f"Response: {status_code} in {duration:.1f}ms")
        
        return response


class TimingMiddleware(RequestMiddleware, ResponseMiddleware):
    """
    Middleware for tracking request timing
    """
    
    async def process_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Add timing information to request"""
        return {
            "method": method,
            "url": url,
            "headers": headers,
            "params": params,
            "data": data,
            "start_time": time.time(),
            **kwargs
        }
    
    async def process_response(
        self,
        response: Dict[str, Any],
        request_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Add timing information to response"""
        start_time = request_context.get("start_time", time.time())
        duration_ms = (time.time() - start_time) * 1000
        
        response["timing"] = {
            "duration_ms": duration_ms,
            "start_time": start_time,
            "end_time": time.time()
        }
        
        return response


class HeaderMiddleware(RequestMiddleware):
    """
    Middleware for adding default headers to requests
    """
    
    def __init__(self, default_headers: Optional[Dict[str, str]] = None):
        self.default_headers = default_headers or {}
    
    async def process_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Add default headers to request"""
        # Merge default headers with request headers
        merged_headers = self.default_headers.copy()
        merged_headers.update(headers)
        
        return {
            "method": method,
            "url": url,
            "headers": merged_headers,
            "params": params,
            "data": data,
            **kwargs
        }


class RetryMiddleware(RequestMiddleware):
    """
    Middleware for handling request retries
    """
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    async def process_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Add retry configuration to request"""
        return {
            "method": method,
            "url": url,
            "headers": headers,
            "params": params,
            "data": data,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            **kwargs
        }
