"""
HTTP client components for shared API system

This module provides configurable HTTP clients and related utilities
for making API requests with comprehensive error handling, retry logic,
and authentication support.
"""

from .client import ConfigurableHTTPClient
from .auth import Authentication<PERSON>andler
from .middleware import RequestMiddleware, ResponseMiddleware

__all__ = [
    "ConfigurableHTTPClient",
    "AuthenticationHandler", 
    "RequestMiddleware",
    "ResponseMiddleware",
]
