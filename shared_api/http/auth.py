"""
Authentication Handler for Shared API System

Provides authentication handling functionality for the shared API system.
"""

import base64
import logging
from typing import Dict, Any, Optional

from ..core.constants import AuthenticationType
from ..core.exceptions import AuthenticationError

logger = logging.getLogger(__name__)


class AuthenticationHandler:
    """
    Handles authentication for API requests
    
    Supports multiple authentication types and provides
    a unified interface for building authentication headers.
    """
    
    @staticmethod
    def build_auth_headers(auth_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Build authentication headers based on configuration
        
        Args:
            auth_config: Authentication configuration dictionary
            
        Returns:
            Dictionary of authentication headers
        """
        headers = {}
        
        if not auth_config:
            return headers
        
        auth_type = auth_config.get("type")
        
        if not auth_type:
            return headers
        
        try:
            if auth_type == AuthenticationType.API_KEY:
                api_key = auth_config.get("api_key")
                header_name = auth_config.get("api_key_header", "X-API-Key")
                if api_key:
                    headers[header_name] = api_key
            
            elif auth_type == AuthenticationType.BEARER_TOKEN:
                token = auth_config.get("bearer_token")
                if token:
                    headers["Authorization"] = f"Bearer {token}"
            
            elif auth_type == AuthenticationType.BASIC_AUTH:
                username = auth_config.get("username")
                password = auth_config.get("password")
                if username and password:
                    credentials = base64.b64encode(
                        f"{username}:{password}".encode()
                    ).decode()
                    headers["Authorization"] = f"Basic {credentials}"
            
            elif auth_type == AuthenticationType.CUSTOM_HEADER:
                custom_headers = auth_config.get("custom_headers", {})
                headers.update(custom_headers)
            
            elif auth_type == AuthenticationType.OAUTH2:
                access_token = auth_config.get("access_token")
                if access_token:
                    headers["Authorization"] = f"Bearer {access_token}"
        
        except Exception as e:
            logger.warning(f"Failed to build auth headers for type {auth_type}: {e}")
        
        return headers
    
    @staticmethod
    def mask_sensitive_data(headers: Dict[str, str]) -> Dict[str, str]:
        """
        Mask sensitive data in headers for logging
        
        Args:
            headers: Headers dictionary
            
        Returns:
            Headers with sensitive values masked
        """
        masked_headers = {}
        sensitive_keys = {
            'authorization', 'x-api-key', 'api-key', 'token',
            'bearer', 'auth', 'secret', 'password'
        }
        
        for key, value in headers.items():
            key_lower = key.lower()
            is_sensitive = any(sensitive_key in key_lower for sensitive_key in sensitive_keys)
            
            if is_sensitive and value:
                if len(value) > 8:
                    masked_headers[key] = f"{value[:4]}***{value[-4:]}"
                else:
                    masked_headers[key] = "***"
            else:
                masked_headers[key] = value
        
        return masked_headers
