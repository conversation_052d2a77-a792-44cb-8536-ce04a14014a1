"""
Example: Adding a New API

This example shows how to add a completely new API using the shared API system.
It demonstrates the configuration-driven approach that makes adding new APIs simple.
"""

from typing import Optional, Dict, Any

from ..config.api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from ..core.constants import HTTPMethod, AuthenticationType


def create_new_api_configuration(
    api_name: str = "example_api",
    base_url: str = "https://api.example.com/v1",
    api_key: Optional[str] = None,
    environment: str = "development"
) -> APIConfiguration:
    """
    Create configuration for a new API
    
    This example shows how to configure a typical REST API with:
    - CRUD operations
    - API key authentication
    - Standard timeouts and retry logic
    
    Args:
        api_name: Name for the API configuration
        base_url: Base URL for the API
        api_key: API key for authentication
        environment: Environment (development, staging, production)
        
    Returns:
        Configured APIConfiguration for the new API
    """
    
    # Define endpoints for typical CRUD operations
    endpoints = {
        # User management endpoints
        "list_users": EndpointConfiguration(
            name="list_users",
            path="/users",
            method=HTTPMethod.GET,
            description="Get list of users with pagination and filtering"
        ),
        "create_user": EndpointConfiguration(
            name="create_user",
            path="/users",
            method=HTTPMethod.POST,
            description="Create a new user"
        ),
        "get_user": EndpointConfiguration(
            name="get_user",
            path="/users/{id}",
            method=HTTPMethod.GET,
            description="Get user by ID"
        ),
        "update_user": EndpointConfiguration(
            name="update_user",
            path="/users/{id}",
            method=HTTPMethod.PUT,
            description="Update user information"
        ),
        "delete_user": EndpointConfiguration(
            name="delete_user",
            path="/users/{id}",
            method=HTTPMethod.DELETE,
            description="Delete user"
        ),
        
        # Product management endpoints
        "list_products": EndpointConfiguration(
            name="list_products",
            path="/products",
            method=HTTPMethod.GET,
            description="Get list of products with filtering"
        ),
        "create_product": EndpointConfiguration(
            name="create_product",
            path="/products",
            method=HTTPMethod.POST,
            description="Create a new product"
        ),
        "get_product": EndpointConfiguration(
            name="get_product",
            path="/products/{id}",
            method=HTTPMethod.GET,
            description="Get product by ID"
        ),
        
        # Order management endpoints
        "list_orders": EndpointConfiguration(
            name="list_orders",
            path="/orders",
            method=HTTPMethod.GET,
            description="Get list of orders"
        ),
        "create_order": EndpointConfiguration(
            name="create_order",
            path="/orders",
            method=HTTPMethod.POST,
            description="Create a new order"
        ),
        "get_order": EndpointConfiguration(
            name="get_order",
            path="/orders/{id}",
            method=HTTPMethod.GET,
            description="Get order by ID"
        ),
        
        # Health and status endpoints
        "health_check": EndpointConfiguration(
            name="health_check",
            path="/health",
            method=HTTPMethod.GET,
            description="API health check endpoint"
        ),
        "api_status": EndpointConfiguration(
            name="api_status",
            path="/status",
            method=HTTPMethod.GET,
            description="Get API status and version information"
        ),
    }
    
    # Set up authentication
    auth = AuthenticationConfiguration(
        type=AuthenticationType.API_KEY if api_key else AuthenticationType.NONE,
        api_key=api_key,
        api_key_header="X-API-Key",
    )
    
    # Standard headers for REST API
    default_headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": f"SharedAPIClient/{api_name}/1.0",
    }
    
    # Timeout configuration
    timeout = TimeoutConfiguration(
        connect=10,
        read=30,
        total=60
    )
    
    # Retry configuration
    retry = RetryConfiguration(
        max_attempts=3,
        delay=1.0,
        backoff_factor=2.0,  # Exponential backoff
        retry_on_status=[500, 502, 503, 504, 429]
    )
    
    # Create the configuration
    config = APIConfiguration(
        name=api_name,
        base_url=base_url,
        endpoints=endpoints,
        authentication=auth,
        default_headers=default_headers,
        timeout=timeout,
        retry=retry,
        description=f"Example API configuration for {api_name}",
        version="1.0",
        environment=environment,
    )
    
    return config


def create_payment_api_configuration(
    base_url: str = "https://payments.example.com/v2",
    api_key: Optional[str] = None,
    environment: str = "development"
) -> APIConfiguration:
    """
    Example: Payment API configuration
    
    Shows how to configure a specialized API with specific requirements.
    """
    
    endpoints = {
        "process_payment": EndpointConfiguration(
            name="process_payment",
            path="/payments",
            method=HTTPMethod.POST,
            description="Process a payment transaction"
        ),
        "get_payment": EndpointConfiguration(
            name="get_payment",
            path="/payments/{id}",
            method=HTTPMethod.GET,
            description="Get payment details by ID"
        ),
        "refund_payment": EndpointConfiguration(
            name="refund_payment",
            path="/payments/{id}/refund",
            method=HTTPMethod.POST,
            description="Refund a payment"
        ),
        "list_payments": EndpointConfiguration(
            name="list_payments",
            path="/payments",
            method=HTTPMethod.GET,
            description="List payments with filtering"
        ),
    }
    
    auth = AuthenticationConfiguration(
        type=AuthenticationType.API_KEY,
        api_key=api_key,
        api_key_header="Authorization",  # Some APIs use Authorization header for API keys
    )
    
    # Payment APIs often need longer timeouts
    timeout = TimeoutConfiguration(
        connect=15,
        read=60,
        total=90
    )
    
    # More aggressive retry for payment APIs
    retry = RetryConfiguration(
        max_attempts=5,
        delay=2.0,
        backoff_factor=2.0,
        retry_on_status=[500, 502, 503, 504]  # Don't retry 429 for payments
    )
    
    config = APIConfiguration(
        name="payment_api",
        base_url=base_url,
        endpoints=endpoints,
        authentication=auth,
        timeout=timeout,
        retry=retry,
        description="Payment processing API",
        version="2.0",
        environment=environment,
    )
    
    return config


# Example configuration dictionary for JSON/YAML export
NEW_API_CONFIG_EXAMPLE = {
    "name": "example_api",
    "base_url": "https://api.example.com/v1",
    "description": "Example API configuration",
    "version": "1.0",
    "environment": "development",
    "endpoints": {
        "list_users": {
            "name": "list_users",
            "path": "/users",
            "method": "GET",
            "description": "Get list of users"
        },
        "create_user": {
            "name": "create_user",
            "path": "/users",
            "method": "POST",
            "description": "Create a new user"
        },
        "get_user": {
            "name": "get_user",
            "path": "/users/{id}",
            "method": "GET",
            "description": "Get user by ID"
        }
    },
    "authentication": {
        "type": "api_key",
        "api_key_header": "X-API-Key",
        "custom_headers": {}
    },
    "default_headers": {
        "Content-Type": "application/json",
        "Accept": "application/json"
    },
    "timeout": {
        "connect": 10,
        "read": 30,
        "total": 60
    },
    "retry": {
        "max_attempts": 3,
        "delay": 1.0,
        "backoff_factor": 2.0,
        "retry_on_status": [500, 502, 503, 504, 429]
    }
}


async def new_api_usage_example():
    """
    Example of using a new API with the shared system
    """
    from ..config.client_factory import api_client_factory
    
    # Create configuration
    config = create_new_api_configuration(
        api_name="my_api",
        base_url="https://api.myservice.com/v1",
        api_key="your-api-key-here"
    )
    
    # Create and use client
    async with api_client_factory.create_client(config) as client:
        # List users
        users = await client.get("list_users", params={
            "page": 1,
            "limit": 10,
            "active": True
        })
        
        # Create a new user
        new_user = await client.post("create_user", data={
            "name": "John Doe",
            "email": "<EMAIL>",
            "role": "user"
        })
        
        # Get user details
        user_id = new_user["data"]["id"]
        user_details = await client.get("get_user", params={"id": user_id})
        
        # List products
        products = await client.get("list_products", params={
            "category": "electronics",
            "in_stock": True
        })
        
        return {
            "users": users,
            "new_user": new_user,
            "user_details": user_details,
            "products": products
        }
