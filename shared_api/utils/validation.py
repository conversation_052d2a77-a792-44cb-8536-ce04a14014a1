"""
Configuration Validation Utilities

Provides validation functionality for API configurations in the shared API system.
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urlparse

from ..core.constants import (
    SUPPORTED_HTTP_METHODS,
    SUPPORTED_AUTH_TYPES,
    URL_PATTERN,
    EMAIL_PATTERN,
)
from ..core.exceptions import ValidationError


class ConfigurationValidator:
    """
    Validates API configurations for correctness and completeness
    """
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """
        Validate URL format
        
        Args:
            url: URL to validate
            
        Returns:
            True if URL is valid
            
        Raises:
            ValidationError: If URL is invalid
        """
        if not url:
            raise ValidationError("URL cannot be empty")
        
        if not re.match(URL_PATTERN, url):
            raise ValidationError(f"Invalid URL format: {url}")
        
        try:
            parsed = urlparse(url)
            if not all([parsed.scheme, parsed.netloc]):
                raise ValidationError(f"URL must have scheme and netloc: {url}")
        except Exception as e:
            raise ValidationError(f"Failed to parse URL {url}: {e}")
        
        return True
    
    @staticmethod
    def validate_http_method(method: str) -> bool:
        """
        Validate HTTP method
        
        Args:
            method: HTTP method to validate
            
        Returns:
            True if method is valid
            
        Raises:
            ValidationError: If method is invalid
        """
        if not method:
            raise ValidationError("HTTP method cannot be empty")
        
        if method.upper() not in SUPPORTED_HTTP_METHODS:
            raise ValidationError(
                f"Unsupported HTTP method: {method}. "
                f"Supported methods: {', '.join(SUPPORTED_HTTP_METHODS)}"
            )
        
        return True
    
    @staticmethod
    def validate_auth_type(auth_type: str) -> bool:
        """
        Validate authentication type
        
        Args:
            auth_type: Authentication type to validate
            
        Returns:
            True if auth type is valid
            
        Raises:
            ValidationError: If auth type is invalid
        """
        if not auth_type:
            raise ValidationError("Authentication type cannot be empty")
        
        if auth_type not in SUPPORTED_AUTH_TYPES:
            raise ValidationError(
                f"Unsupported authentication type: {auth_type}. "
                f"Supported types: {', '.join(SUPPORTED_AUTH_TYPES)}"
            )
        
        return True
    
    @staticmethod
    def validate_timeout_config(config: Dict[str, Any]) -> bool:
        """
        Validate timeout configuration
        
        Args:
            config: Timeout configuration dictionary
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValidationError: If configuration is invalid
        """
        required_fields = ["connect", "read", "total"]
        
        for field in required_fields:
            if field not in config:
                raise ValidationError(f"Missing required timeout field: {field}")
            
            value = config[field]
            if not isinstance(value, (int, float)) or value <= 0:
                raise ValidationError(f"Timeout {field} must be a positive number")
        
        # Validate relationships
        if config["total"] < max(config["connect"], config["read"]):
            raise ValidationError(
                "Total timeout must be >= connect and read timeouts"
            )
        
        return True
    
    @staticmethod
    def validate_retry_config(config: Dict[str, Any]) -> bool:
        """
        Validate retry configuration
        
        Args:
            config: Retry configuration dictionary
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValidationError: If configuration is invalid
        """
        if "max_attempts" in config:
            max_attempts = config["max_attempts"]
            if not isinstance(max_attempts, int) or max_attempts < 0:
                raise ValidationError("max_attempts must be a non-negative integer")
        
        if "delay" in config:
            delay = config["delay"]
            if not isinstance(delay, (int, float)) or delay < 0:
                raise ValidationError("delay must be a non-negative number")
        
        if "backoff_factor" in config:
            backoff_factor = config["backoff_factor"]
            if not isinstance(backoff_factor, (int, float)) or backoff_factor < 0:
                raise ValidationError("backoff_factor must be a non-negative number")
        
        if "retry_on_status" in config:
            retry_on_status = config["retry_on_status"]
            if not isinstance(retry_on_status, list):
                raise ValidationError("retry_on_status must be a list")
            
            for status in retry_on_status:
                if not isinstance(status, int) or status < 100 or status > 599:
                    raise ValidationError(f"Invalid HTTP status code: {status}")
        
        return True
    
    @staticmethod
    def validate_endpoint_config(config: Dict[str, Any]) -> bool:
        """
        Validate endpoint configuration
        
        Args:
            config: Endpoint configuration dictionary
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValidationError: If configuration is invalid
        """
        required_fields = ["name", "path", "method"]
        
        for field in required_fields:
            if field not in config:
                raise ValidationError(f"Missing required endpoint field: {field}")
        
        # Validate name
        name = config["name"]
        if not isinstance(name, str) or not name.strip():
            raise ValidationError("Endpoint name must be a non-empty string")
        
        # Validate path
        path = config["path"]
        if not isinstance(path, str) or not path:
            raise ValidationError("Endpoint path must be a non-empty string")
        
        # Validate method
        ConfigurationValidator.validate_http_method(config["method"])
        
        return True
    
    @staticmethod
    def validate_auth_config(config: Dict[str, Any]) -> bool:
        """
        Validate authentication configuration
        
        Args:
            config: Authentication configuration dictionary
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValidationError: If configuration is invalid
        """
        if not config:
            return True  # No auth is valid
        
        auth_type = config.get("type")
        if not auth_type:
            raise ValidationError("Authentication type is required")
        
        ConfigurationValidator.validate_auth_type(auth_type)
        
        # Validate type-specific requirements
        if auth_type == "api_key":
            if not config.get("api_key"):
                raise ValidationError("API key is required for API key authentication")
        
        elif auth_type == "bearer_token":
            if not config.get("bearer_token"):
                raise ValidationError("Bearer token is required for bearer token authentication")
        
        elif auth_type == "basic_auth":
            if not config.get("username") or not config.get("password"):
                raise ValidationError("Username and password are required for basic authentication")
        
        elif auth_type == "oauth2":
            if not config.get("access_token"):
                raise ValidationError("Access token is required for OAuth2 authentication")
        
        return True
    
    @staticmethod
    def validate_api_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate complete API configuration
        
        Args:
            config: Complete API configuration dictionary
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            # Validate required fields
            required_fields = ["name", "base_url"]
            for field in required_fields:
                if field not in config:
                    errors.append(f"Missing required field: {field}")
            
            if errors:
                return False, errors
            
            # Validate name
            name = config["name"]
            if not isinstance(name, str) or len(name.strip()) < 2:
                errors.append("API name must be at least 2 characters long")
            
            # Validate base URL
            try:
                ConfigurationValidator.validate_url(config["base_url"])
            except ValidationError as e:
                errors.append(str(e))
            
            # Validate endpoints
            if "endpoints" in config:
                endpoints = config["endpoints"]
                if not isinstance(endpoints, dict):
                    errors.append("Endpoints must be a dictionary")
                else:
                    for endpoint_name, endpoint_config in endpoints.items():
                        try:
                            ConfigurationValidator.validate_endpoint_config(endpoint_config)
                        except ValidationError as e:
                            errors.append(f"Endpoint '{endpoint_name}': {e}")
            
            # Validate authentication
            if "authentication" in config:
                try:
                    ConfigurationValidator.validate_auth_config(config["authentication"])
                except ValidationError as e:
                    errors.append(f"Authentication: {e}")
            
            # Validate timeout
            if "timeout" in config:
                try:
                    ConfigurationValidator.validate_timeout_config(config["timeout"])
                except ValidationError as e:
                    errors.append(f"Timeout: {e}")
            
            # Validate retry
            if "retry" in config:
                try:
                    ConfigurationValidator.validate_retry_config(config["retry"])
                except ValidationError as e:
                    errors.append(f"Retry: {e}")
            
            return len(errors) == 0, errors
        
        except Exception as e:
            errors.append(f"Unexpected validation error: {e}")
            return False, errors
