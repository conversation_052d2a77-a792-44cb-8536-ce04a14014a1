"""
Helper utilities for shared API system

Common utility functions used throughout the shared API system.
"""

import re
from typing import Dict, Any, Optional, Union
from urllib.parse import urlparse, urljoin


def mask_sensitive_data(data: Dict[str, Any], sensitive_keys: Optional[set] = None) -> Dict[str, Any]:
    """
    Mask sensitive data in dictionaries for logging
    
    Args:
        data: Dictionary that may contain sensitive data
        sensitive_keys: Set of keys to mask (default: common sensitive keys)
        
    Returns:
        Dictionary with sensitive values masked
    """
    if sensitive_keys is None:
        sensitive_keys = {
            'password', 'token', 'key', 'secret', 'auth', 'authorization',
            'api_key', 'bearer_token', 'access_token', 'refresh_token',
            'client_secret', 'private_key', 'credential', 'credentials'
        }
    
    masked_data = {}
    for key, value in data.items():
        key_lower = key.lower()
        
        # Check if key contains any sensitive keywords
        is_sensitive = any(sensitive_key in key_lower for sensitive_key in sensitive_keys)
        
        if is_sensitive and isinstance(value, str) and value:
            # Mask the value, showing only first and last 2 characters for longer strings
            if len(value) > 8:
                masked_data[key] = f"{value[:2]}***{value[-2:]}"
            else:
                masked_data[key] = "***"
        elif isinstance(value, dict):
            # Recursively mask nested dictionaries
            masked_data[key] = mask_sensitive_data(value, sensitive_keys)
        else:
            masked_data[key] = value
    
    return masked_data


def merge_headers(*header_dicts: Dict[str, str]) -> Dict[str, str]:
    """
    Merge multiple header dictionaries, with later ones taking precedence
    
    Args:
        *header_dicts: Variable number of header dictionaries
        
    Returns:
        Merged headers dictionary
    """
    merged = {}
    for headers in header_dicts:
        if headers:
            merged.update(headers)
    return merged


def parse_url(url: str) -> Dict[str, str]:
    """
    Parse URL into components
    
    Args:
        url: URL to parse
        
    Returns:
        Dictionary with URL components
    """
    parsed = urlparse(url)
    return {
        'scheme': parsed.scheme,
        'netloc': parsed.netloc,
        'hostname': parsed.hostname,
        'port': parsed.port,
        'path': parsed.path,
        'params': parsed.params,
        'query': parsed.query,
        'fragment': parsed.fragment,
    }


def format_response_time(time_ms: float) -> str:
    """
    Format response time for display
    
    Args:
        time_ms: Response time in milliseconds
        
    Returns:
        Formatted response time string
    """
    if time_ms < 1000:
        return f"{time_ms:.1f}ms"
    else:
        return f"{time_ms/1000:.2f}s"


def sanitize_config_for_logging(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize configuration for safe logging
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Sanitized configuration safe for logging
    """
    sanitized = config.copy()
    
    # Remove or mask sensitive fields
    sensitive_fields = {
        'api_key', 'bearer_token', 'password', 'secret', 'token',
        'credentials', 'auth', 'authorization'
    }
    
    for field in sensitive_fields:
        if field in sanitized:
            if isinstance(sanitized[field], str) and sanitized[field]:
                sanitized[field] = "***"
            elif isinstance(sanitized[field], dict):
                sanitized[field] = mask_sensitive_data(sanitized[field])
    
    # Recursively sanitize nested configurations
    for key, value in sanitized.items():
        if isinstance(value, dict):
            sanitized[key] = sanitize_config_for_logging(value)
        elif isinstance(value, list):
            sanitized[key] = [
                sanitize_config_for_logging(item) if isinstance(item, dict) else item
                for item in value
            ]
    
    return sanitized


def validate_url(url: str) -> bool:
    """
    Validate URL format
    
    Args:
        url: URL to validate
        
    Returns:
        True if URL is valid, False otherwise
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def build_endpoint_url(base_url: str, endpoint_path: str) -> str:
    """
    Build full endpoint URL from base URL and path
    
    Args:
        base_url: Base URL
        endpoint_path: Endpoint path
        
    Returns:
        Full endpoint URL
    """
    # Ensure base_url doesn't end with slash and endpoint_path starts with slash
    base_url = base_url.rstrip('/')
    if not endpoint_path.startswith('/'):
        endpoint_path = '/' + endpoint_path
    
    return urljoin(base_url + '/', endpoint_path.lstrip('/'))


def extract_error_message(response_text: str, max_length: int = 200) -> str:
    """
    Extract meaningful error message from response text
    
    Args:
        response_text: Raw response text
        max_length: Maximum length of extracted message
        
    Returns:
        Extracted error message
    """
    if not response_text:
        return "No error message available"
    
    # Try to extract JSON error message
    try:
        import json
        data = json.loads(response_text)
        
        # Common error message fields
        error_fields = ['error', 'message', 'detail', 'description', 'error_description']
        
        for field in error_fields:
            if field in data and data[field]:
                message = str(data[field])
                return message[:max_length] if len(message) > max_length else message
    except (json.JSONDecodeError, TypeError):
        pass
    
    # Fallback to truncated raw text
    return response_text[:max_length] if len(response_text) > max_length else response_text


def normalize_header_name(header_name: str) -> str:
    """
    Normalize header name to standard format
    
    Args:
        header_name: Header name to normalize
        
    Returns:
        Normalized header name
    """
    return '-'.join(word.capitalize() for word in header_name.lower().split('-'))


def is_json_content_type(content_type: str) -> bool:
    """
    Check if content type indicates JSON
    
    Args:
        content_type: Content type string
        
    Returns:
        True if content type indicates JSON
    """
    if not content_type:
        return False
    
    content_type = content_type.lower()
    return 'application/json' in content_type or content_type.endswith('+json')


def generate_request_id() -> str:
    """
    Generate unique request ID
    
    Returns:
        Unique request ID string
    """
    import uuid
    return str(uuid.uuid4())


def calculate_retry_delay(attempt: int, base_delay: float, backoff_factor: float, max_delay: float = 60.0) -> float:
    """
    Calculate retry delay with exponential backoff
    
    Args:
        attempt: Current attempt number (0-based)
        base_delay: Base delay in seconds
        backoff_factor: Backoff multiplier
        max_delay: Maximum delay in seconds
        
    Returns:
        Calculated delay in seconds
    """
    delay = base_delay * (backoff_factor ** attempt)
    return min(delay, max_delay)
