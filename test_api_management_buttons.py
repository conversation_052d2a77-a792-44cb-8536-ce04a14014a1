#!/usr/bin/env python3
"""
Test script to verify API management button functionality
"""

import asyncio
import sys
from unittest.mock import AsyncMock, MagicMock

# Add the project root to the path
sys.path.insert(0, '.')

def test_ui_methods():
    """Test that all UI methods exist and can be called"""
    from admin.ui.api_management_ui import APIManagementUI
    
    ui = APIManagementUI()
    
    # Test data
    api_details = {
        "admin_info": {
            "name": "test_api",
            "display_name": "Test API",
            "base_url": "https://api.example.com",
            "environment": "development",
            "status": "active",
            "enabled": True,
            "category": "general",
            "created_at": "2024-01-01T00:00:00Z"
        },
        "shared_config": {
            "timeout": {"connect": 10, "read": 30, "total": 60},
            "retry": {"max_attempts": 3, "delay": 1.0, "backoff_factor": 2.0}
        },
        "endpoints": {
            "list": {"name": "list", "path": "/list", "method": "GET", "description": "List items"},
            "get": {"name": "get", "path": "/get", "method": "GET", "description": "Get item"}
        }
    }
    
    audit_logs = [
        {
            "timestamp": "2024-01-01T00:00:00Z",
            "action": "created",
            "user": "admin",
            "details": "API configuration created"
        }
    ]
    
    api_configs = [
        {
            "name": "test_api",
            "display_name": "Test API",
            "base_url": "https://api.example.com",
            "environment": "development",
            "enabled": True,
            "category": "general",
            "health_status": "healthy"
        }
    ]
    
    print("Testing UI methods...")
    
    # Test new UI methods
    try:
        # API Details buttons
        result = ui.format_api_edit_menu(api_details)
        assert "Edit API: Test API" in result
        print("✅ format_api_edit_menu works")
        
        keyboard = ui.create_api_edit_keyboard("test_api")
        assert keyboard.inline_keyboard
        print("✅ create_api_edit_keyboard works")
        
        result = ui.format_api_settings(api_details)
        assert "API Settings: Test API" in result
        print("✅ format_api_settings works")
        
        keyboard = ui.create_api_settings_keyboard("test_api")
        assert keyboard.inline_keyboard
        print("✅ create_api_settings_keyboard works")
        
        result = ui.format_api_audit_log("test_api", audit_logs)
        assert "Audit Log: test_api" in result
        print("✅ format_api_audit_log works")
        
        keyboard = ui.create_api_audit_keyboard("test_api")
        assert keyboard.inline_keyboard
        print("✅ create_api_audit_keyboard works")
        
        result = ui.format_api_export(api_details)
        assert "Export API: Test API" in result
        print("✅ format_api_export works")
        
        keyboard = ui.create_api_export_keyboard("test_api")
        assert keyboard.inline_keyboard
        print("✅ create_api_export_keyboard works")
        
        result = ui.format_endpoint_test_menu(api_details)
        assert "Test Endpoints: Test API" in result
        print("✅ format_endpoint_test_menu works")
        
        keyboard = ui.create_endpoint_test_keyboard("test_api", api_details["endpoints"])
        assert keyboard.inline_keyboard
        print("✅ create_endpoint_test_keyboard works")
        
        # Filtering methods
        result = ui.format_filter_menu()
        assert "Filter & Search APIs" in result
        print("✅ format_filter_menu works")
        
        keyboard = ui.create_filter_keyboard()
        assert keyboard.inline_keyboard
        print("✅ create_filter_keyboard works")
        
        result = ui.format_search_prompt()
        assert "Search APIs" in result
        print("✅ format_search_prompt works")
        
        result = ui.format_search_results("test", api_configs)
        assert "Search Results for: 'test'" in result
        print("✅ format_search_results works")
        
        keyboard = ui.create_search_results_keyboard(api_configs, "test")
        assert keyboard.inline_keyboard
        print("✅ create_search_results_keyboard works")
        
        result = ui.format_filtered_api_list(api_configs, "status", "enabled")
        assert "Filtered APIs - Status: enabled" in result
        print("✅ format_filtered_api_list works")
        
        keyboard = ui.create_filtered_list_keyboard(api_configs, "status", "enabled")
        assert keyboard.inline_keyboard
        print("✅ create_filtered_list_keyboard works")
        
        print("\n🎉 All UI methods are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing UI methods: {e}")
        return False

def test_callback_data_patterns():
    """Test that callback data patterns are consistent"""
    from admin.ui.api_management_ui import APIManagementUI
    
    ui = APIManagementUI()
    
    print("\nTesting callback data patterns...")
    
    # Test API details keyboard
    keyboard = ui.create_api_details_keyboard("test_api")
    callback_data_found = []
    
    for row in keyboard.inline_keyboard:
        for button in row:
            callback_data_found.append(button.callback_data)
    
    expected_patterns = [
        "api_test:test_api",
        "api_edit:test_api", 
        "api_test_endpoints:test_api",
        "api_settings:test_api",
        "api_audit:test_api",
        "api_export:test_api",
        "api_list"
    ]
    
    for pattern in expected_patterns:
        if pattern in callback_data_found:
            print(f"✅ Found callback pattern: {pattern}")
        else:
            print(f"❌ Missing callback pattern: {pattern}")
            return False
    
    print("🎉 All callback data patterns are correct!")
    return True

def main():
    """Run all tests"""
    print("🧪 Testing API Management Button Functionality\n")
    
    success = True
    
    # Test UI methods
    if not test_ui_methods():
        success = False
    
    # Test callback patterns
    if not test_callback_data_patterns():
        success = False
    
    if success:
        print("\n✅ All tests passed! The API management buttons should work correctly.")
        print("\n📋 Summary of fixes implemented:")
        print("• Added missing callback handlers for edit, settings, audit, export, test endpoints")
        print("• Implemented all missing UI formatting methods")
        print("• Added comprehensive filtering and search functionality")
        print("• Enhanced API list with filter/search buttons")
        print("• All callback handlers are properly registered")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
