"""Adapters that expose API v2 browse functionality through v1-style interfaces."""

from __future__ import annotations

from typing import Any, Dict, Optional

from api_v2.services.browse_service import APIV2BrowseService, APIV2BrowseParams, APIV2BrowseResponse
from services.external_api_service import APIResponse, APIOperation, ListItemsParams


class APIV2ExternalBrowseAdapter:
    """Adapter that makes API v2 behave like the legacy ExternalAPIService."""

    def __init__(self, api_config) -> None:
        self._api_config = api_config
        config_kwargs = self._prepare_config_kwargs(api_config)
        self._service = APIV2BrowseService(config_kwargs=config_kwargs)

    def _prepare_config_kwargs(self, api_config) -> Dict[str, Any]:
        login_token = ""
        session_cookies: Optional[Dict[str, str]] = None
        default_headers: Dict[str, str] = dict(
            getattr(api_config, "default_headers", {}) or {}
        )

        auth_config = getattr(api_config, "auth_config", None)
        auth = getattr(api_config, "authentication", None)

        if auth_config is not None:
            login_token = getattr(auth_config, "bearer_token", "") or ""
            try:
                auth_headers = auth_config.get_headers()
            except Exception:
                auth_headers = {}
            for key, value in (auth_headers or {}).items():
                if value:
                    default_headers[key] = value

            custom_headers = getattr(auth_config, "custom_headers", {}) or {}
            if isinstance(custom_headers, dict):
                default_headers.update(custom_headers)
        elif auth is not None:
            if isinstance(auth, dict):
                login_token = auth.get("bearer_token", "") or auth.get("token", "") or ""
                custom_headers = auth.get("custom_headers", {}) or {}
                if isinstance(custom_headers, dict):
                    default_headers.update(custom_headers)
            else:
                login_token = getattr(auth, "bearer_token", "") or ""
                custom_headers = getattr(auth, "custom_headers", {}) or {}
                if isinstance(custom_headers, dict):
                    default_headers.update(custom_headers)

        session_cookies = self._extract_session_cookies(default_headers)

        environment = getattr(api_config, "environment", "production")

        return {
            "base_url": getattr(api_config, "base_url", ""),
            "login_token": login_token,
            "session_cookies": session_cookies,
            "environment": environment,
            "default_headers": default_headers or None,
        }

    @staticmethod
    def _extract_session_cookies(headers: Dict[str, str]) -> Optional[Dict[str, str]]:
        cookie_header = None
        for key in ("cookie", "Cookie"):
            if key in headers:
                cookie_header = headers.pop(key)
                break

        if not cookie_header:
            return None

        cookies: Dict[str, str] = {}
        for pair in cookie_header.split(";"):
            if "=" not in pair:
                continue
            name, value = pair.strip().split("=", 1)
            if name:
                cookies[name.strip()] = value.strip()
        return cookies or None

    @staticmethod
    def _convert_params(params: Optional[ListItemsParams]) -> APIV2BrowseParams:
        if params is None:
            return APIV2BrowseParams()

        return APIV2BrowseParams(
            page=params.page,
            limit=params.limit,
            base=params.base,
            bank=params.bank,
            bin=params.bin,
            country=params.country,
            state=params.state,
            city=params.city,
            brand=params.brand,
            type=params.type,
            level=getattr(params, "level", ""),
            zip=params.zip,
            price_from=params.price_from,
            price_to=params.price_to,
            zip_check=params.zip_check,
            address=params.address,
            phone=params.phone,
            email=params.email,
            without_cvv=params.without_cvv,
            refundable=params.refundable,
            expire_this_month=params.expire_this_month,
            dob=params.dob,
            ssn=params.ssn,
            mmn=params.mmn,
            ip=params.ip,
            dl=params.dl,
            ua=params.ua,
            discount=params.discount,
        )

    @staticmethod
    def _to_api_response(response: APIV2BrowseResponse, operation: APIOperation) -> APIResponse:
        return APIResponse(
            success=response.success,
            data=response.data,
            error=response.error,
            status_code=response.status_code,
            raw_response=response.raw_response,
            operation=operation,
        )

    async def list_items(
        self,
        params: Optional[ListItemsParams] = None,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        browse_params = self._convert_params(params)
        response = await self._service.list_items(browse_params, user_id=user_id)
        return self._to_api_response(response, APIOperation.LIST_ITEMS)

    async def get_filters(
        self,
        filter_name: str,
        params: Optional[ListItemsParams] = None,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        browse_params = self._convert_params(params)
        response = await self._service.get_filters(filter_name, browse_params, user_id=user_id)
        return self._to_api_response(response, APIOperation.FILTERS)

    async def list_orders(
        self,
        page: int = 1,
        limit: int = 10,
    ) -> APIResponse:
        response = await self._service.list_orders(page=page, limit=limit)
        return self._to_api_response(response, APIOperation.LIST_ORDERS)

    async def close(self) -> None:
        await self._service.close()
