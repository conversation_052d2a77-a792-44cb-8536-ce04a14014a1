"""Configuration factory for API v2 (BASE 2)."""

from __future__ import annotations

from typing import Optional, Dict

from models.api import APIEnvironment

from shared_api.config.api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from shared_api.core.constants import HTTPMethod, AuthenticationType


DEFAULT_BASE_URL = "https://ronaldo-club.to/api/cards/vhq/"


def _derive_origin(base_url: str) -> str:
    """Derive site origin from API base URL."""
    if "/api" in base_url:
        return base_url.split("/api", 1)[0]
    return base_url.rstrip("/")


def create_api_v2_configuration(
    *,
    base_url: str = DEFAULT_BASE_URL,
    login_token: Optional[str] = None,
    session_cookies: Optional[Dict[str, str]] = None,
    environment: str | APIEnvironment = "production",
    default_headers: Optional[Dict[str, str]] = None,
) -> APIConfiguration:
    """Create the shared API configuration for API v2/BASE 2."""
    normalized_base = base_url.rstrip("/")
    origin = _derive_origin(normalized_base)
    env_value = environment.value if isinstance(environment, APIEnvironment) else str(environment)

    endpoints = {
        "list_items": EndpointConfiguration(
            name="list_items",
            path="/list",
            method=HTTPMethod.POST,
            description="List BIN cards with filtering, search, and pagination (BASE 2)",
        ),
        "filters": EndpointConfiguration(
            name="filters",
            path="/filters",
            method=HTTPMethod.GET,
            description="Retrieve filter metadata for browse UI (BASE 2)",
        ),
        "orders": EndpointConfiguration(
            name="orders",
            path="/orders",
            method=HTTPMethod.GET,
            description="Fetch paginated purchase history scoped to BASE 2",
        ),
        "check_order": EndpointConfiguration(
            name="check_order",
            path="/check",
            method=HTTPMethod.POST,
            description="Verify order details within BASE 2",
        ),
    }

    if login_token:
        auth = AuthenticationConfiguration(
            type=AuthenticationType.BEARER_TOKEN,
            bearer_token=login_token,
        )
    else:
        auth = AuthenticationConfiguration(
            type=AuthenticationType.NONE,
        )

    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.6",
        "content-type": "application/json",
        "origin": origin,
        "referer": f"{origin}/store/cards/vhq",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "sec-gpc": "1",
        "user-agent": (
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/********* Safari/537.36"
        ),
    }

    if default_headers:
        headers.update(default_headers)

    if session_cookies:
        cookie_header = "; ".join(f"{k}={v}" for k, v in session_cookies.items())
        headers["cookie"] = cookie_header

    timeout = TimeoutConfiguration(connect=10, read=30, total=60)
    retry = RetryConfiguration(
        max_attempts=3,
        delay=1.0,
        backoff_factor=1.0,
        retry_on_status=[500, 502, 503, 504, 429],
    )

    return APIConfiguration(
        name="api2",
        base_url=normalized_base,
        endpoints=endpoints,
        authentication=auth,
        default_headers=headers,
        timeout=timeout,
        retry=retry,
        description="API v2/BASE 2 - Secondary BIN browse API (VHQ endpoint)",
        version="2.0",
        environment=env_value,
    )
