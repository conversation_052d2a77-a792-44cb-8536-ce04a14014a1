"""
Common handler utilities for Telegram bot handlers

This module provides reusable functions for common handler patterns:
- User validation and lookup
- Keyboard creation utilities
- Error handling patterns
- Message formatting helpers
"""

from __future__ import annotations

import logging
from typing import Optional, List, Dict, Any, Tuple

from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from services.user_service import UserService
from models.user import User
from utils.texts import DEMO_WATERMARK

logger = logging.getLogger(__name__)


class HandlerError(Exception):
    """Custom exception for handler errors"""
    pass


class UserValidationHelper:
    """Helper class for user validation and lookup operations"""

    def __init__(self):
        # Defer service creation until first use so we don't require a DB connection at import time.
        self._user_service: UserService | None = None

    @property
    def user_service(self) -> UserService:
        """Return a cached user service instance, creating it on first use."""
        if self._user_service is None:
            self._user_service = UserService()
        return self._user_service
    
    async def get_user_from_callback(self, callback: CallbackQuery) -> Optional[User]:
        """
        Get user from callback query with validation
        
        Args:
            callback: Telegram callback query
            
        Returns:
            User object if found, None otherwise
        """
        try:
            user = callback.from_user
            if not user:
                await callback.answer("❌ User not found", show_alert=True)
                return None
            
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            if not db_user:
                await callback.answer("❌ User not found", show_alert=True)
                return None
                
            return db_user
            
        except Exception as e:
            logger.error(f"Error getting user from callback: {e}")
            await callback.answer("❌ Error occurred", show_alert=True)
            return None
    
    async def get_user_from_message(self, message: Message) -> Optional[User]:
        """
        Get user from message with validation
        
        Args:
            message: Telegram message
            
        Returns:
            User object if found, None otherwise
        """
        try:
            user = message.from_user
            if not user:
                return None
            
            db_user = await self.user_service.get_user_by_telegram_id(user.id)
            return db_user
            
        except Exception as e:
            logger.error(f"Error getting user from message: {e}")
            return None


class KeyboardHelper:
    """Helper class for creating common keyboard layouts"""
    
    @staticmethod
    def create_back_to_main_keyboard() -> InlineKeyboardMarkup:
        """Create a simple back to main menu keyboard"""
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(text="🔙 Main Menu", callback_data="menu:main")]
            ]
        )
    
    @staticmethod
    def create_cart_navigation_keyboard() -> InlineKeyboardMarkup:
        """Create cart navigation keyboard"""
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(text="🛒 View Cart", callback_data="local:cart:view"),
                    InlineKeyboardButton(text="🔎 Browse Cards", callback_data="menu:browse")
                ],
                [InlineKeyboardButton(text="🔙 Main Menu", callback_data="menu:main")]
            ]
        )
    
    @staticmethod
    def create_empty_cart_keyboard() -> InlineKeyboardMarkup:
        """Create keyboard for empty cart state"""
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(text="🔎 Browse Cards", callback_data="menu:browse")],
                [InlineKeyboardButton(text="🔙 Main Menu", callback_data="menu:main")]
            ]
        )
    
    @staticmethod
    def create_cart_actions_keyboard() -> InlineKeyboardMarkup:
        """Create keyboard for cart with items"""
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(text="💳 Checkout", callback_data="local:cart:checkout"),
                    InlineKeyboardButton(text="🗑️ Clear Cart", callback_data="local:cart:clear")
                ],
                [
                    InlineKeyboardButton(text="✏️ Edit Items", callback_data="local:cart:edit"),
                    InlineKeyboardButton(text="🔄 Refresh", callback_data="local:cart:view")
                ],
                [InlineKeyboardButton(text="🛒 Browse Catalog", callback_data="menu:browse")],
                [InlineKeyboardButton(text="🔙 Back", callback_data="menu:main")]
            ]
        )
    
    @staticmethod
    def create_item_edit_keyboard(card_id: int) -> InlineKeyboardMarkup:
        """Create keyboard for editing cart item"""
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add 1",
                        callback_data=f"local:cart:qty_change:{card_id}:+1"
                    ),
                    InlineKeyboardButton(
                        text="➖ Remove 1",
                        callback_data=f"local:cart:qty_change:{card_id}:-1"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🗑️ Remove Item",
                        callback_data=f"local:cart:remove_item:{card_id}"
                    )
                ],
                [InlineKeyboardButton(text="🔙 Back", callback_data="local:cart:edit")]
            ]
        )


class ErrorHandler:
    """Helper class for standardized error handling"""
    
    @staticmethod
    async def handle_callback_error(
        callback: CallbackQuery, 
        error: Exception, 
        operation: str = "operation"
    ) -> None:
        """
        Handle callback query errors with standardized response
        
        Args:
            callback: Telegram callback query
            error: Exception that occurred
            operation: Description of the operation that failed
        """
        logger.error(f"Error in {operation}: {error}")
        try:
            await callback.answer("❌ Error occurred", show_alert=True)
        except Exception as e:
            logger.error(f"Failed to send error response: {e}")
    
    @staticmethod
    async def handle_message_error(
        message: Message,
        error: Exception,
        operation: str = "operation"
    ) -> None:
        """
        Handle message errors with standardized response
        
        Args:
            message: Telegram message
            error: Exception that occurred
            operation: Description of the operation that failed
        """
        logger.error(f"Error in {operation}: {error}")
        try:
            await message.reply("❌ An error occurred. Please try again.")
        except Exception as e:
            logger.error(f"Failed to send error response: {e}")


class MessageFormatter:
    """Helper class for message formatting"""
    
    @staticmethod
    def format_cart_empty_message() -> str:
        """Format empty cart message"""
        return (
            "🛒 <b>Cart Empty</b>\n\n"
            "Your cart is empty. Add some items first!"
            + DEMO_WATERMARK
        )
    
    @staticmethod
    def format_success_message(title: str, message: str) -> str:
        """Format success message with demo watermark"""
        return f"✅ <b>{title}</b>\n\n{message}" + DEMO_WATERMARK
    
    @staticmethod
    def format_error_message(title: str, message: str) -> str:
        """Format error message with demo watermark"""
        return f"❌ <b>{title}</b>\n\n{message}" + DEMO_WATERMARK
    
    @staticmethod
    def format_info_message(title: str, message: str) -> str:
        """Format info message with demo watermark"""
        return f"ℹ️ <b>{title}</b>\n\n{message}" + DEMO_WATERMARK
    
    @staticmethod
    def add_demo_watermark(text: str) -> str:
        """Add demo watermark to any text"""
        return text + DEMO_WATERMARK


class CallbackDataParser:
    """Helper class for parsing callback data"""
    
    @staticmethod
    def parse_callback_data(callback_data: str, expected_parts: int) -> Optional[List[str]]:
        """
        Parse callback data and validate part count
        
        Args:
            callback_data: Callback data string
            expected_parts: Expected number of parts after splitting
            
        Returns:
            List of parts if valid, None otherwise
        """
        if not callback_data:
            return None
            
        parts = callback_data.split(":")
        if len(parts) != expected_parts:
            return None
            
        return parts
    
    @staticmethod
    def extract_id_from_callback(callback_data: str, prefix: str) -> Optional[int]:
        """
        Extract ID from callback data with given prefix
        
        Args:
            callback_data: Callback data string
            prefix: Expected prefix (e.g., "cart:edit:")
            
        Returns:
            Extracted ID if valid, None otherwise
        """
        if not callback_data or not callback_data.startswith(prefix):
            return None
            
        try:
            id_str = callback_data[len(prefix):]
            return int(id_str)
        except (ValueError, IndexError):
            return None


# Convenience instances for easy import
user_validator = UserValidationHelper()
keyboard_helper = KeyboardHelper()
error_handler = ErrorHandler()
message_formatter = MessageFormatter()
callback_parser = CallbackDataParser()
