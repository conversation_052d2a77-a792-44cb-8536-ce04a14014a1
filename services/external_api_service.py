"""
Comprehensive External API Integration Service

This service provides a consolidated interface for all external API operations
including list, add_to_cart, view_cart, delete_from_cart, and user authentication.
It integrates with the existing admin panel for configuration management.

Based on external API examples and documentation.
"""

from __future__ import annotations

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import json
from dataclasses import dataclass, field
from enum import Enum
import time
from pathlib import Path
import os

from aiohttp import ClientTimeout, ClientSession
from collections import OrderedDict
from urllib.parse import urlencode

from api_v1.services.api_config import get_api_config_service
from api_v1.services.http_client import get_http_client, HTTPRequest, HTTPMethod
from api_v1.utils.authentication import AuthenticationHelper
from admin.models.api_config_storage import (
    AdminAPIConfiguration,
    get_admin_api_service,
)
from shared_api.config.api_config import APIConfiguration as SharedAPIConfiguration
from config.settings import get_settings
from utils.logging import get_logger
from utils.performance import monitor_performance
from utils.api_logging import get_api_logger, LogLevel

logger = get_logger(__name__)
api_logger = get_api_logger("external_api_service", LogLevel.DEBUG)


class APIOperation(str, Enum):
    """Supported API operations"""

    LIST_ITEMS = "list_items"
    ADD_TO_CART = "add_to_cart"
    VIEW_CART = "view_cart"
    DELETE_FROM_CART = "delete_from_cart"
    GET_USER_INFO = "get_user_info"
    CHECKOUT = "checkout"
    LIST_ORDERS = "list_orders"
    CHECK_ORDER = "check_order"
    FILTERS = "filters"


@dataclass
class APIResponse:
    """Standardized API response wrapper"""

    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    raw_response: Optional[str] = None
    operation: Optional[APIOperation] = None
    execution_time: Optional[float] = None


@dataclass
class ListItemsParams:
    """Parameters for list items operation"""

    page: int = 1
    limit: int = 10
    base: str = ""
    bank: str = ""
    bin: str = ""
    country: str = ""
    state: str = ""
    city: str = ""
    brand: str = ""
    type: str = ""
    level: str = ""
    zip: str = ""
    price_from: float = 0
    price_to: float = 500
    zip_check: bool = False
    address: bool = False
    phone: bool = False
    email: bool = False
    without_cvv: bool = False
    refundable: bool = False
    expire_this_month: bool = False
    dob: bool = False
    ssn: bool = False
    mmn: bool = False
    ip: bool = False
    dl: bool = False
    ua: bool = False
    discount: bool = False


@dataclass
class ExternalAPIConfig:
    """Configuration for external API service"""

    base_url: str
    login_token: str
    session_cookies: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


class ExternalAPIService:
    """
    Comprehensive external API service for all operations

    This service handles:
    - List items with filtering
    - Add items to cart
    - View cart contents
    - Delete items from cart
    - User authentication and session management
    """

    def __init__(self):
        self.settings = get_settings()
        try:
            self.api_config_service = get_api_config_service()
        except Exception:
            # Allow running without DB/admin panel; fall back to defaults
            self.api_config_service = None

        try:
            self.admin_api_service = get_admin_api_service()
        except Exception:
            self.admin_api_service = None
        # HTTP session not used directly; requests go through unified HTTP client
        self._config_cache: Optional[ExternalAPIConfig] = None
        self._cache_expiry: Optional[datetime] = None
        self._cache_ttl_minutes = 30

        # Dynamic cookie storage for server-provided cookies
        self._dynamic_cookies: Dict[str, str] = {}
        # Persisted auth state (survives restarts) to avoid editing .env constantly
        self._auth_state_path = (
            Path(__file__).resolve().parent.parent / "config" / "external_auth.json"
        )
        self._persisted_login_token: str = ""
        self._load_persisted_auth_state()
        self._http_client = get_http_client()
        self._auth_helper = AuthenticationHelper()

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Parameters are required by the async context manager protocol
        await self.close()

    async def close(self):
        """Close shared HTTP client to avoid unclosed session warnings"""
        try:
            await self._http_client.close()
        except Exception:
            pass

    # HTTP session is managed by unified HTTP client; no local ensure_session

    async def _get_api_config(self) -> Optional[ExternalAPIConfig]:
        """Get API configuration with caching"""
        try:
            # Check cache validity
            if (
                self._config_cache
                and self._cache_expiry
                and datetime.now(timezone.utc) < self._cache_expiry
            ):
                return self._config_cache

            # Prefer admin-managed shared API configuration if available
            admin_config = await self._load_admin_managed_config()
            if admin_config:
                return admin_config

            # Load configuration from API v1 unified config service by name (legacy fallback)
            api_config = None
            if self.api_config_service is not None:
                api_config = await self.api_config_service.get_api_config_by_name(
                    "api1_external_cart", decrypt_sensitive=True
                )
            if not api_config:
                # Fallback to simplified name used by API v1 defaults
                if self.api_config_service is not None:
                    api_config = await self.api_config_service.get_api_config_by_name(
                        "api1", decrypt_sensitive=True
                    )

            if not api_config:
                logger.debug(
                    "No api1_external_cart API configuration found, using default"
                )
                return self._get_default_config()

            # Build configuration details from API v1 config (authentication and headers)
            # Note: API v1 stores auth in api_config.authentication; cookies are expected via env
            token = ""
            try:
                auth = getattr(api_config, "authentication", None)
                if auth and getattr(auth, "bearer_token", None):
                    token = auth.bearer_token or ""
            except Exception:
                token = ""

            # Prefer a persisted login token if none set via admin config
            if not token and self._persisted_login_token:
                token = self._persisted_login_token
            # Fallback to env
            if not token:
                token = getattr(self.settings, "EXTERNAL_LOGIN_TOKEN", "")

            # Compose headers using defaults plus any config defaults
            headers = self._get_default_headers()
            try:
                if getattr(api_config, "default_headers", None):
                    headers.update(api_config.default_headers)
            except Exception:
                pass

            # Build cookies merged from persisted auth state and environment variables
            cookies = self._get_env_cookies()

            config = ExternalAPIConfig(
                base_url=api_config.base_url,
                login_token=token,
                session_cookies=cookies,
                headers=headers,
                timeout=30,
                max_retries=3,
                retry_delay=1.0,
            )

            # Update cache
            self._config_cache = config
            self._cache_expiry = datetime.now(timezone.utc) + timedelta(
                minutes=self._cache_ttl_minutes
            )

            return config

        except Exception as e:
            logger.error(f"Error loading API configuration: {e}")
            return self._get_default_config()

    async def _load_admin_managed_config(self) -> Optional[ExternalAPIConfig]:
        """Load configuration managed through the admin shared API system."""
        if not getattr(self, "admin_api_service", None):
            return None

        try:
            candidate_names = [
                "api1_external_cart",
                "api1",
                "api_v1",
            ]

            for name in candidate_names:
                admin_config: Optional[AdminAPIConfiguration] = (
                    await self.admin_api_service.get_api_config(name)
                )
                if not admin_config:
                    continue

                if not getattr(admin_config, "enabled", True):
                    logger.debug(
                        "Admin API configuration '%s' is disabled; skipping",
                        name,
                    )
                    continue

                shared_config = admin_config.to_shared_config()
                external_config = self._build_external_config_from_shared(shared_config)

                if external_config:
                    self._config_cache = external_config
                    self._cache_expiry = datetime.now(timezone.utc) + timedelta(
                        minutes=self._cache_ttl_minutes
                    )
                    logger.debug(
                        "Loaded API configuration '%s' from admin management system",
                        name,
                    )
                    return external_config

        except Exception as admin_error:
            logger.warning(
                "Failed to load admin-managed API configuration: %s",
                admin_error,
            )

        return None

    def _build_external_config_from_shared(
        self, shared_config: SharedAPIConfiguration
    ) -> ExternalAPIConfig:
        """Convert shared API configuration into ExternalAPIService config."""

        # Start with baseline headers (lower-case for internal consistency)
        headers = {k.lower(): v for k, v in self._get_default_headers().items() if v}

        default_headers = getattr(shared_config, "default_headers", {}) or {}
        for key, value in default_headers.items():
            if value:
                headers[key.lower()] = value

        # Authentication headers and login token extraction
        login_token = ""
        auth_config = getattr(shared_config, "auth_config", None)
        if auth_config:
            login_token = getattr(auth_config, "bearer_token", "") or ""
            auth_headers = auth_config.get_headers()
            for key, value in (auth_headers or {}).items():
                if value:
                    headers[key.lower()] = value

            custom_headers = getattr(auth_config, "custom_headers", {}) or {}
            for key, value in custom_headers.items():
                if value:
                    headers[key.lower()] = value
        else:
            authentication = getattr(shared_config, "authentication", None)
            if isinstance(authentication, dict):
                login_token = authentication.get("bearer_token", "") or authentication.get(
                    "token", ""
                )
                for key, value in authentication.items():
                    if isinstance(value, str) and value:
                        headers[key.lower()] = value
            elif authentication is not None:
                login_token = getattr(authentication, "bearer_token", "") or ""
                custom_headers = getattr(authentication, "custom_headers", {}) or {}
                for key, value in custom_headers.items():
                    if value:
                        headers[key.lower()] = value

        # Extract cookies from headers if present
        cookie_header = None
        if "cookie" in headers:
            cookie_header = headers.pop("cookie")

        cookies = self._get_env_cookies()
        if cookie_header:
            cookies.update(self._parse_cookie_header(cookie_header))

        if not login_token:
            login_token = self._persisted_login_token or getattr(
                self.settings, "EXTERNAL_LOGIN_TOKEN", ""
            )

        timeout_config = getattr(shared_config, "timeout_config", None)
        timeout = getattr(timeout_config, "total", 60)

        retry_config = getattr(shared_config, "retry_config", None)
        max_retries = getattr(retry_config, "max_attempts", 3)
        retry_delay = getattr(retry_config, "delay", 1.0)

        return ExternalAPIConfig(
            base_url=shared_config.base_url,
            login_token=login_token,
            session_cookies=cookies,
            headers=headers,
            timeout=timeout,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

    @staticmethod
    def _parse_cookie_header(cookie_header: str) -> Dict[str, str]:
        """Parse Cookie header string into dictionary."""
        cookies: Dict[str, str] = {}
        for part in cookie_header.split(";"):
            if "=" not in part:
                continue
            name, value = part.split("=", 1)
            name = name.strip()
            value = value.strip()
            if name and value:
                cookies[name] = value
        return cookies

    def _get_default_config(self) -> ExternalAPIConfig:
        """Get default configuration based on working demo patterns"""
        return ExternalAPIConfig(
            base_url="https://ronaldo-club.to/api",
            login_token=(
                self._persisted_login_token
                or getattr(self.settings, "EXTERNAL_LOGIN_TOKEN", "")
            ),
            session_cookies=self._get_env_cookies(),
            headers=self._get_default_headers(),
            timeout=30,
            max_retries=3,
            retry_delay=1.0,
        )

    def _get_env_cookies(self) -> Dict[str, str]:
        """Build cookie defaults merged with any persisted cookies"""
        cookies = {
            "__ddg1_": getattr(self.settings, "EXTERNAL_DDG1", ""),
            "__ddg8_": getattr(self.settings, "EXTERNAL_DDG8", ""),
            "__ddg9_": getattr(self.settings, "EXTERNAL_DDG9", ""),
            "__ddg10_": getattr(self.settings, "EXTERNAL_DDG10", ""),
            "_ga": getattr(self.settings, "EXTERNAL_GA", ""),
            "_ga_KZWCRF57VT": getattr(self.settings, "EXTERNAL_GA_KZWCRF57VT", ""),
            "testcookie": "1",
        }
        # Merge persisted cookies (persisted values win if non-empty)
        try:
            persisted = self._load_persisted_auth_state()
            pc = (persisted or {}).get("cookies", {})
            for k, v in pc.items():
                if v:
                    cookies[k] = v
        except Exception:
            pass
        return cookies

    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers based on demo examples

        Important: Do NOT set Content-Length here. Let aiohttp compute it
        based on the actual request body. A hard-coded Content-Length: 0
        breaks POST requests with JSON bodies (add_to_cart, checkout).
        """
        return {
            "accept": "application/json, text/plain, */*",
            "accept-language": "en-US,en;q=0.9",
            "origin": "https://ronaldo-club.to",
            "priority": "u=1, i",
            "referer": "https://ronaldo-club.to/store/cards/hq",
            "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Brave";v="140"',
            "sec-ch-ua-mobile": "?1",
            "sec-ch-ua-platform": '"Android"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sec-gpc": "1",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/140.0.0.0 Mobile Safari/537.36",
        }

    def _build_headers(
        self, config: ExternalAPIConfig, operation: APIOperation
    ) -> Dict[str, str]:
        """Build headers for specific operation"""
        # Base headers from configuration
        headers = config.headers.copy()

        # Operation-specific header adjustments
        if operation == APIOperation.LIST_ITEMS:
            headers.update(
                {
                    "referer": "https://ronaldo-club.to/store/cards/hq",
                }
            )
        elif operation == APIOperation.ADD_TO_CART:
            headers.update(
                {
                    "content-type": "application/json",
                    "referer": "https://ronaldo-club.to/store/cards/hq",
                }
            )
            # Ensure we don't carry a stale content-length
            headers.pop("content-length", None)
        elif operation == APIOperation.VIEW_CART:
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            # Remove content-type for GET request
            headers.pop("content-type", None)
            headers.pop("content-length", None)
        elif operation == APIOperation.DELETE_FROM_CART:
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            # Remove content-type for DELETE request
            headers.pop("content-type", None)
            headers.pop("content-length", None)
        elif operation == APIOperation.GET_USER_INFO:
            headers.update(
                {
                    "content-type": "application/json",
                    "referer": "https://ronaldo-club.to/",
                }
            )
            headers.pop("content-length", None)
        elif operation == APIOperation.CHECKOUT:
            headers.update({"referer": "https://ronaldo-club.to/store/cart"})
            # For checkout, send no content-type and no content-length (no body)
            headers.pop("content-type", None)
            headers.pop("content-length", None)
        elif operation == APIOperation.LIST_ORDERS:
            headers.update(
                {
                    "referer": "https://ronaldo-club.to/orders/cards/hq",
                }
            )
            # GET request, ensure no conflicting headers
            headers.pop("content-type", None)
            headers.pop("content-length", None)
        elif operation == APIOperation.CHECK_ORDER:
            headers.update(
                {
                    "content-type": "application/json",
                    "referer": "https://ronaldo-club.to/orders/cards/hq",
                }
            )
            headers.pop("content-length", None)
        elif operation == APIOperation.FILTERS:
            headers.update(
                {
                    "content-type": "application/json",
                    "referer": "https://ronaldo-club.to/store/cards/hq",
                }
            )
            headers.pop("content-length", None)

        return headers

    def _build_cookies(self, config: ExternalAPIConfig) -> Dict[str, str]:
        """Build cookies with login token and dynamic server-provided cookies"""
        cookies = config.session_cookies.copy()

        # Update with any dynamic cookies provided by the server
        cookies.update(self._dynamic_cookies)

        # Ensure login token is included
        if config.login_token:
            cookies["loginToken"] = config.login_token
            # Track latest login token in memory
            self._persisted_login_token = config.login_token
            # Ensure it's persisted so next run doesn't require .env edits
            self._persist_auth_state()

        # Ensure testcookie is set
        cookies["testcookie"] = "1"
        # Drop empty cookie values to avoid sending blank cookies
        cookies = {k: v for k, v in cookies.items() if v}

        return cookies

    def _update_dynamic_cookies(self, response_headers: Dict[str, str]) -> None:
        """Update dynamic cookies from server response and persist auth state"""
        try:
            set_cookie_header = response_headers.get("Set-Cookie", "")
            if not set_cookie_header:
                return

            # Parse Set-Cookie header for __ddg* cookies
            import re

            # Look for __ddg1_, __ddg8_, __ddg9_, __ddg10_ cookies
            ddg_patterns = [
                r"__ddg1_=([^;]+)",
                r"__ddg8_=([^;]+)",
                r"__ddg9_=([^;]+)",
                r"__ddg10_=([^;]+)",
                r"_ga=([^;]+)",
                r"_ga_KZWCRF57VT=([^;]+)",
                r"loginToken=([^;]+)",
            ]

            for pattern in ddg_patterns:
                match = re.search(pattern, set_cookie_header)
                if match:
                    # Extract cookie name from pattern
                    if "__ddg1_" in pattern:
                        cookie_name = "__ddg1_"
                    elif "__ddg8_" in pattern:
                        cookie_name = "__ddg8_"
                    elif "__ddg9_" in pattern:
                        cookie_name = "__ddg9_"
                    elif "__ddg10_" in pattern:
                        cookie_name = "__ddg10_"
                    elif pattern.startswith("_ga="):
                        cookie_name = "_ga"
                    elif "_ga_KZWCRF57VT" in pattern:
                        cookie_name = "_ga_KZWCRF57VT"
                    elif "loginToken" in pattern:
                        cookie_name = "loginToken"
                    else:
                        continue

                    cookie_value = match.group(1)
                    if cookie_name == "loginToken":
                        self._persisted_login_token = cookie_value
                    else:
                        self._dynamic_cookies[cookie_name] = cookie_value
                    # Avoid logging sensitive cookie values
                    logger.debug(f"Updated dynamic cookie: {cookie_name}")

            # Persist any updated auth state
            self._persist_auth_state()

        except Exception as e:
            logger.debug(f"Error updating dynamic cookies: {e}")

    # --- Persistence helpers ---
    def _load_persisted_auth_state(self) -> Optional[Dict[str, Any]]:
        """Load persisted auth state from disk into memory.

        Returns the dict if available so callers can read values.
        """
        try:
            path = self._auth_state_path
            if not path.exists():
                return None
            with path.open("r", encoding="utf-8") as f:
                data = json.load(f)
            # Apply into memory
            cookies = data.get("cookies", {}) or {}
            if cookies:
                self._dynamic_cookies.update({k: v for k, v in cookies.items() if v})
            token = data.get("loginToken") or data.get("login_token") or ""
            if token:
                self._persisted_login_token = token
            return data
        except Exception:
            return None

    def _persist_auth_state(self) -> None:
        """Persist current auth state (loginToken + cookies) to disk.

        This avoids re-entering EXTERNAL_* env values repeatedly.
        """
        try:
            path = self._auth_state_path
            path.parent.mkdir(parents=True, exist_ok=True)
            data = {
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "loginToken": self._persisted_login_token or "",
                "cookies": {
                    k: v
                    for k, v in self._dynamic_cookies.items()
                    if v and k in {"__ddg1_", "__ddg8_", "__ddg9_", "__ddg10_", "_ga", "_ga_KZWCRF57VT"}
                },
            }
            with path.open("w", encoding="utf-8") as f:
                json.dump(data, f, indent=2)
        except Exception:
            # Non-fatal
            pass

    @staticmethod
    def _format_numeric(value: Any) -> str:
        """Format numeric values to align with the demo request patterns."""
        if value is None:
            return ""
        if isinstance(value, (int, float)):
            as_float = float(value)
            if as_float.is_integer():
                return str(int(as_float))
            return f"{as_float:.2f}".rstrip("0").rstrip(".")
        try:
            as_float = float(str(value))
            if as_float.is_integer():
                return str(int(as_float))
            return f"{as_float:.2f}".rstrip("0").rstrip(".")
        except (ValueError, TypeError):
            return str(value)

    def _build_filter_query_pairs(self, params: ListItemsParams) -> list[tuple[str, str]]:
        """Build ordered key/value pairs shared by list and filter endpoints."""

        def add(key: str, value: Any) -> None:
            if value is None:
                text = ""
            else:
                text = str(value)
            pairs.append((key, text))

        pairs: list[tuple[str, str]] = []
        add("base", params.base or "")
        add("bank", params.bank or "")
        add("bin", params.bin or "")
        add("country", params.country or "")
        add("state", params.state or "")
        add("city", params.city or "")
        add("brand", params.brand or "")
        add("type", params.type or "")
        add("level", params.level or "")
        add("zip", params.zip or "")
        add("priceFrom", self._format_numeric(params.price_from))
        add("priceTo", self._format_numeric(params.price_to))
        add("zipCheck", "true" if params.zip_check else "false")
        add("address", "true" if params.address else "false")
        add("phone", "true" if params.phone else "false")
        add("email", "true" if params.email else "false")
        add("withoutcvv", "true" if params.without_cvv else "false")
        add("refundable", "true" if params.refundable else "false")
        add("expirethismonth", "true" if params.expire_this_month else "false")
        add("dob", "true" if params.dob else "false")
        add("ssn", "true" if params.ssn else "false")
        add("mmn", "true" if params.mmn else "false")
        add("ip", "true" if params.ip else "false")
        add("dl", "true" if params.dl else "false")
        add("ua", "true" if params.ua else "false")
        add("discount", "true" if params.discount else "false")
        return pairs

    def _build_filter_query_string(
        self, params: ListItemsParams, *, for_body: bool = False
    ) -> str:
        """Build query string for request URL or request body."""
        pairs = self._build_filter_query_pairs(params)
        if for_body:
            return "&".join(f"{key}={value}" for key, value in pairs)
        return urlencode(pairs, doseq=True, safe="")

    def _build_filters_url(
        self, config: ExternalAPIConfig, params: ListItemsParams
    ) -> str:
        base_url = f"{config.base_url}/cards/hq/filters"
        query_string = self._build_filter_query_string(params)
        return f"{base_url}?{query_string}"

    def _build_list_url(
        self, config: ExternalAPIConfig, params: ListItemsParams
    ) -> str:
        """Build URL for list items operation"""
        base_url = f"{config.base_url}/cards/hq/list"
        pairs = [("page", str(int(params.page))), ("limit", str(int(params.limit)))]
        pairs.extend(self._build_filter_query_pairs(params))
        return f"{base_url}?{urlencode(pairs, doseq=True, safe='')}"

    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Dict[str, str],
        cookies: Dict[str, str],
        operation: APIOperation,
        json_data: Optional[Dict[str, Any]] = None,
        max_retries: int = 3,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        """Make HTTP request with comprehensive logging and retry logic"""
        start_time = time.time()
        # Create logging context
        context = api_logger.create_context(user_id=user_id, operation=operation.value)

        for attempt in range(max_retries + 1):
            try:
                # Log authentication context
                auth_method = (
                    "bearer_token"
                    if headers.get("Authorization")
                    else "session_cookies" if cookies else "none"
                )
                token_valid = bool(
                    headers.get("Authorization") or cookies.get("loginToken")
                )

                api_logger.log_authentication_context(
                    context=context,
                    auth_method=auth_method,
                    token_valid=token_valid,
                    user_permissions=["api_access"] if token_valid else [],
                    rate_limit_info={},
                )

                # Log the API request
                api_logger.log_request(
                    context=context,
                    method=method,
                    url=url,
                    headers=headers,
                    query_params=None,  # URL already contains query params
                    body=json_data,
                    timeout=30.0,
                    retry_count=attempt,
                )

                # Suppress verbose request debug logs; response logging handled centrally

                # Use unified HTTP client
                request = HTTPRequest(
                    method=HTTPMethod(method),
                    url=url,
                    headers=headers,
                    json_data=json_data,
                    cookies=cookies,
                    timeout=30,
                    max_retries=max_retries,
                )
                http_resp = await self._http_client.request(
                    request,
                    auth_config=None,
                    user_id=user_id,
                    operation=operation.value,
                )

                execution_time = time.time() - start_time
                raw_response = http_resp.text or ""
                response_headers = http_resp.headers or {}
                class _RespLike:
                    status = http_resp.status_code or 0
                    reason = ""  # not provided
                response = _RespLike()

                # Response details are logged by API logger; avoid duplicate noisy logs

                # Update dynamic cookies from server response
                self._update_dynamic_cookies(response_headers)

                # Log the API response
                api_logger.log_response(
                    context=context,
                    status_code=response.status,
                    status_message=response.reason or "Unknown",
                    headers=response_headers,
                    body=raw_response,
                    error_type="http_error" if response.status >= 400 else None,
                    error_message=(
                        f"HTTP {response.status}"
                        if response.status >= 400
                        else None
                    ),
                )

                # Parse JSON response
                try:
                    data = (
                        http_resp.data
                        if http_resp.data is not None
                        else (json.loads(raw_response) if raw_response else {})
                    )
                except json.JSONDecodeError:
                    logger.warning(
                        f"Failed to parse JSON response: {raw_response[:200]}"
                    )

                    # Log JSON parsing error
                    api_logger.log_response(
                        context=context,
                        status_code=response.status,
                        status_message="JSON Parse Error",
                        headers=response_headers,
                        body=raw_response[:1000],
                        error_type="json_parse_error",
                        error_message="Failed to parse JSON response",
                    )

                    data = {"raw_response": raw_response}

                # Special handling for 403 Forbidden errors
                if response.status == 403:
                    api_logger.log_403_error_context(
                        context=context,
                        endpoint=url,
                        auth_method=auth_method,
                        token_status="valid" if token_valid else "invalid",
                        user_role="user" if token_valid else "anonymous",
                        required_permissions=["api_access", operation.value],
                        rate_limit_headers={
                            k: v
                            for k, v in response_headers.items()
                            if k.lower().startswith(("x-rate", "x-ratelimit", "retry-after"))
                        },
                    )

                # Determine success based on status code and response content
                # CRITICAL FIX: Don't default to True if "success" field is missing
                if response.status == 200 and isinstance(data, dict):
                    api_success = data.get("success")
                    if api_success is True:
                        success = True
                    elif api_success is False:
                        success = False
                    else:
                        success = False
                        logger.warning(
                            f"API response missing or invalid 'success' field: {data}"
                        )
                else:
                    success = False

                return APIResponse(
                    success=success,
                    data=data,
                    status_code=response.status,
                    raw_response=raw_response,
                    operation=operation,
                    execution_time=execution_time,
                    error=(
                        None if success else f"HTTP {response.status}: {raw_response[:200]}"
                    ),
                )

            except asyncio.TimeoutError:
                error_msg = f"Request timeout on attempt {attempt + 1}"
                logger.warning(error_msg)
                if attempt == max_retries:
                    return APIResponse(
                        success=False,
                        error=f"Request timeout after {max_retries + 1} attempts",
                        operation=operation,
                        execution_time=time.time() - start_time,
                    )
                await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

            except Exception as e:
                error_msg = f"Request error on attempt {attempt + 1}: {str(e)}"
                logger.error(error_msg)
                if attempt == max_retries:
                    return APIResponse(
                        success=False,
                        error=f"Request failed after {max_retries + 1} attempts: {str(e)}",
                        operation=operation,
                        execution_time=time.time() - start_time,
                    )
                await asyncio.sleep(1.0 * (attempt + 1))  # Exponential backoff

    @monitor_performance("list_items")
    async def list_items(
        self, params: Optional[ListItemsParams] = None, user_id: Optional[str] = None
    ) -> APIResponse:
        """
        List items with filtering parameters

        Based on demo/list.py - Uses POST request with query parameters
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.LIST_ITEMS,
                )

            # Use default parameters if none provided
            if params is None:
                params = ListItemsParams()

            # Build request components
            url = self._build_list_url(config, params)
            headers = self._build_headers(config, APIOperation.LIST_ITEMS)
            cookies = self._build_cookies(config)

            # Keep logs concise; response logger prints results
            logger.debug(
                f"Listing items with params: page={params.page}, limit={params.limit}"
            )

            # Make POST request (as per demo example)
            # Important: send no body (Content-Length: 0) to mirror site behavior
            return await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.LIST_ITEMS,
                user_id=user_id,
                json_data=None,
            )

        except Exception as e:
            logger.error(f"Error in list_items: {e}")
            return APIResponse(
                success=False,
                error=f"List items operation failed: {str(e)}",
                operation=APIOperation.LIST_ITEMS,
            )

    @monitor_performance("get_filters")
    async def get_filters(
        self,
        filter_name: str,
        params: Optional[ListItemsParams] = None,
        user_id: Optional[str] = None,
    ) -> APIResponse:
        """Fetch filter options using the working demo request structure."""

        try:
            if not filter_name:
                return APIResponse(
                    success=False,
                    error="Filter name is required",
                    operation=APIOperation.FILTERS,
                )

            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.FILTERS,
                )

            if params is None:
                params = ListItemsParams()

            url = self._build_filters_url(config, params)
            headers = self._build_headers(config, APIOperation.FILTERS)
            cookies = self._build_cookies(config)
            payload = {
                "name": filter_name,
                "stringfilterData": self._build_filter_query_string(
                    params, for_body=True
                ),
            }

            return await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.FILTERS,
                json_data=payload,
                user_id=user_id,
            )

        except Exception as e:
            logger.error(f"Error in get_filters: {e}")
            return APIResponse(
                success=False,
                error=f"Filter operation failed: {str(e)}",
                operation=APIOperation.FILTERS,
            )

    @monitor_performance("list_orders")
    async def list_orders(self, page: int = 1, limit: int = 10) -> APIResponse:
        """
        List recent orders from the external API.

        Mirrors demo/orders.py behavior using GET with querystring params.
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.LIST_ORDERS,
                )

            # Build URL with query parameters
            url = f"{config.base_url}/cards/hq/orders?page={int(page)}&&limit={int(limit)}"
            headers = self._build_headers(config, APIOperation.LIST_ORDERS)
            cookies = self._build_cookies(config)

            # Perform GET request
            response = await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.LIST_ORDERS,
                json_data=None,
            )
            return response
        except Exception as e:
            logger.error(f"Error in list_orders: {e}")
            return APIResponse(
                success=False,
                error=f"List orders operation failed: {str(e)}",
                operation=APIOperation.LIST_ORDERS,
            )

    @monitor_performance("add_to_cart")
    async def add_to_cart(
        self, item_id: int, product_table_name: str = "Cards"
    ) -> APIResponse:
        """
        Add item to cart

        Based on demo/add_to_cart.py - Uses POST request with JSON payload
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.ADD_TO_CART,
                )

            # Build request components
            url = f"{config.base_url}/cart/"
            headers = self._build_headers(config, APIOperation.ADD_TO_CART)
            cookies = self._build_cookies(config)

            # Payload as per demo example
            payload = {"id": item_id, "product_table_name": product_table_name}

            logger.debug(f"Adding item {item_id} to cart")

            # Make POST request with JSON payload
            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.ADD_TO_CART,
            )

            # Response content is logged centrally; keep a concise summary
            logger.debug(
                f"add_to_cart {response.status_code} -> success={response.success}"
            )

            if not response.success:
                logger.error(f"Add to cart failed for item {item_id}: {response.error}")
                if response.data and isinstance(response.data, dict):
                    # Log any additional error details from the API response
                    error_details = (
                        response.data.get("error")
                        or response.data.get("message")
                        or response.data.get("details")
                    )
                    if error_details:
                        logger.error(f"  API Error Details: {error_details}")

            return response

        except Exception as e:
            logger.error(f"Error in add_to_cart: {e}")
            return APIResponse(
                success=False,
                error=f"Add to cart operation failed: {str(e)}",
                operation=APIOperation.ADD_TO_CART,
            )

    @monitor_performance("view_cart")
    async def view_cart(self) -> APIResponse:
        """
        View cart contents

        Based on demo/view_cart.py - Uses GET request
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.VIEW_CART,
                )

            # Build request components
            url = f"{config.base_url}/cart/"
            headers = self._build_headers(config, APIOperation.VIEW_CART)
            cookies = self._build_cookies(config)

            logger.debug("Viewing cart contents")

            # Make GET request
            return await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.VIEW_CART,
            )

        except Exception as e:
            logger.error(f"Error in view_cart: {e}")
            return APIResponse(
                success=False,
                error=f"View cart operation failed: {str(e)}",
                operation=APIOperation.VIEW_CART,
            )

    @monitor_performance("delete_from_cart")
    async def delete_from_cart(self, cart_item_id: int) -> APIResponse:
        """
        Delete item from cart

        Based on demo/delete_from_cart.py - Uses DELETE request with item ID in URL
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.DELETE_FROM_CART,
                )

            # Build request components - item ID is part of URL
            url = f"{config.base_url}/cart/{cart_item_id}"
            headers = self._build_headers(config, APIOperation.DELETE_FROM_CART)
            cookies = self._build_cookies(config)

            logger.debug(f"Deleting cart item {cart_item_id}")

            # Make DELETE request
            return await self._make_request(
                method="DELETE",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.DELETE_FROM_CART,
            )

        except Exception as e:
            logger.error(f"Error in delete_from_cart: {e}")
            return APIResponse(
                success=False,
                error=f"Delete from cart operation failed: {str(e)}",
                operation=APIOperation.DELETE_FROM_CART,
            )

    @monitor_performance("get_user_info")
    async def get_user_info(self) -> APIResponse:
        """
        Get user information

        Based on demo/getme.py - Uses GET request to user/getme endpoint
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.GET_USER_INFO,
                )

            # Build request components
            url = f"{config.base_url}/user/getme"
            headers = self._build_headers(config, APIOperation.GET_USER_INFO)
            cookies = self._build_cookies(config)

            logger.info("Getting user information")

            # Make GET request
            return await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.GET_USER_INFO,
            )
        except Exception as e:
            logger.error(f"Error in get_user_info: {e}")
            return APIResponse(
                success=False,
                error=f"Get user info operation failed: {str(e)}",
                operation=APIOperation.GET_USER_INFO,
            )

    @monitor_performance("checkout")
    async def checkout(self) -> APIResponse:
        """
        Perform checkout operation

        Based on /checkout/ endpoint - Uses POST request
        """
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.CHECKOUT,
                )

            url = f"{config.base_url}/cart/checkout"
            headers = self._build_headers(config, APIOperation.CHECKOUT)
            cookies = self._build_cookies(config)

            # Validate critical cookie presence
            if not cookies.get("loginToken"):
                logger.error("Checkout aborted: missing loginToken cookie")
                return APIResponse(
                    success=False,
                    error="Missing login token for checkout",
                    operation=APIOperation.CHECKOUT,
                )

            # Log cookie keys (not values) for traceability
            try:
                logger.debug(
                    "Checkout using cookies: " + ", ".join(sorted(cookies.keys()))
                )
            except Exception:
                pass

            logger.info("Executing external checkout")

            response = await self._make_request(
                method="GET",
                url=url,
                headers=headers,
                cookies=cookies,
                operation=APIOperation.CHECKOUT,
                json_data=None,
                max_retries=0,  # Do not retry checkout; run once and report
            )
            if not response.success:
                logger.error(
                    f"Checkout failed: status={response.status_code} "
                    f"body={(response.raw_response or '')[:500]}"
                )
            return response
        except Exception as e:
            logger.error(f"Error in checkout: {e}")
            return APIResponse(
                success=False,
                error=f"Checkout operation failed: {str(e)}",
                operation=APIOperation.CHECKOUT,
            )

        

    # Utility methods for easier integration

    @monitor_performance("check_order")
    async def check_order(self, order_id: int) -> APIResponse:
        """Run the external check API for a specific order id."""
        try:
            config = await self._get_api_config()
            if not config:
                return APIResponse(
                    success=False,
                    error="API configuration not available",
                    operation=APIOperation.CHECK_ORDER,
                )

            url = f"{config.base_url}/cards/hq/check"
            headers = self._build_headers(config, APIOperation.CHECK_ORDER)
            cookies = self._build_cookies(config)

            payload = {"id": int(order_id)}

            response = await self._make_request(
                method="POST",
                url=url,
                headers=headers,
                cookies=cookies,
                json_data=payload,
                operation=APIOperation.CHECK_ORDER,
            )
            return response
        except Exception as e:
            logger.error(f"Error in check_order: {e}")
            return APIResponse(
                success=False,
                error=f"Check order failed: {str(e)}",
                operation=APIOperation.CHECK_ORDER,
            )

    async def is_authenticated(self) -> bool:
        """Check if the current session is authenticated"""
        try:
            response = await self.get_user_info()
            return response.success and response.data and "user" in response.data
        except Exception as e:
            logger.error(f"Error checking authentication: {e}")
            return False

    async def get_cart_total_price(self) -> Optional[float]:
        """Get the total price of items in cart"""
        try:
            response = await self.view_cart()
            if response.success and response.data:
                return response.data.get("totalCartPrice")
            return None
        except Exception as e:
            logger.error(f"Error getting cart total price: {e}")
            return None

    async def get_cart_item_count(self) -> int:
        """Get the number of items in cart"""
        try:
            response = await self.view_cart()
            if response.success and response.data:
                data_items = response.data.get("data", [])
                return len(data_items) if isinstance(data_items, list) else 0
            return 0
        except Exception as e:
            logger.error(f"Error getting cart item count: {e}")
            return 0

    async def clear_configuration_cache(self):
        """Clear the configuration cache to force reload"""
        self._config_cache = None
        self._cache_expiry = None
        logger.info("Configuration cache cleared")

    def get_supported_operations(self) -> List[APIOperation]:
        """Get list of supported API operations"""
        return list(APIOperation)


# Global service instance
_external_api_service: Optional[ExternalAPIService] = None


def get_external_api_service() -> ExternalAPIService:
    """Get the global external API service instance"""
    global _external_api_service
    if _external_api_service is None:
        _external_api_service = ExternalAPIService()
    return _external_api_service


async def close_external_api_service():
    """Close the global external API service instance"""
    global _external_api_service
    if _external_api_service is not None:
        await _external_api_service.close()
        _external_api_service = None
